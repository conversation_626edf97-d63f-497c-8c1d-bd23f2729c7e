package cz.kpsys.portaro.location;

import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.databasestructure.AcquisitionDb;
import cz.kpsys.portaro.databasestructure.ExemplarDb;
import cz.kpsys.portaro.databasestructure.PlacementDb;
import cz.kpsys.portaro.sql.generator.DeleteQuery;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

@FieldDefaults(level = lombok.AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public class LocationDeleter implements Deleter<Location> {

    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public void delete(Location location) {
        DeleteQuery dqRozdelovnik = queryFactory.newDeleteQuery();
        dqRozdelovnik.delete(AcquisitionDb.ROZDELOVNIK.TABLE);
        dqRozdelovnik.where().eq(AcquisitionDb.ROZDELOVNIK.FK_LOKACE, location.getId());
        notAutoCommittingJdbcTemplate.update(dqRozdelovnik.getSql(), dqRozdelovnik.getParamMap());

        DeleteQuery dqRozdelObj = queryFactory.newDeleteQuery();
        dqRozdelObj.delete(AcquisitionDb.ROZDEL_OBJ.TABLE);
        dqRozdelObj.where().eq(AcquisitionDb.ROZDEL_OBJ.FK_LOKACE, location.getId());
        notAutoCommittingJdbcTemplate.update(dqRozdelObj.getSql(), dqRozdelObj.getParamMap());

        DeleteQuery dqRozdelFak = queryFactory.newDeleteQuery();
        dqRozdelFak.delete(AcquisitionDb.ROZDEL_FAK.TABLE);
        dqRozdelFak.where().eq(AcquisitionDb.ROZDEL_FAK.FK_LOKACE, location.getId());
        notAutoCommittingJdbcTemplate.update(dqRozdelFak.getSql(), dqRozdelFak.getParamMap());

        DeleteQuery dqDefPrircisRady = queryFactory.newDeleteQuery();
        dqDefPrircisRady.delete(ExemplarDb.DEF_PRIRCIS_RADY.TABLE);
        dqDefPrircisRady.where().eq(ExemplarDb.DEF_PRIRCIS_RADY.FK_LOKACE, location.getId());
        notAutoCommittingJdbcTemplate.update(dqDefPrircisRady.getSql(), dqDefPrircisRady.getParamMap());

        DeleteQuery dqDefPujcLok = queryFactory.newDeleteQuery();
        dqDefPujcLok.delete(PlacementDb.DEF_PUJC_LOK.TABLE);
        dqDefPujcLok.where().eq(PlacementDb.DEF_PUJC_LOK.FK_LOKACE, location.getId());
        notAutoCommittingJdbcTemplate.update(dqDefPujcLok.getSql(), dqDefPujcLok.getParamMap());

        DeleteQuery dqDefLokace = queryFactory.newDeleteQuery();
        dqDefLokace.delete(PlacementDb.DEF_LOKACE.TABLE);
        dqDefLokace.where().eq(PlacementDb.DEF_LOKACE.ID_LOKACE, location.getId());
        notAutoCommittingJdbcTemplate.update(dqDefLokace.getSql(), dqDefLokace.getParamMap());
    }

}
