dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation(project(":portaro-core"))
    testImplementation(project(":portaro-commons"))
    testImplementation(project(":portaro-commons-containers"))
    testImplementation(project(":portaro-finance"))
    testImplementation(project(":portaro-user"))
    testImplementation(project(":portaro-tcp-client"))
    testImplementation(project(":portaro-sip2-commons"))
    testImplementation(project(":portaro-sip2-server"))
    testImplementation(project(":portaro-sip2-client"))
    testImplementation(project(":portaro-verbisboxer-manager"))
    testImplementation(project(":portaro-database-structure"))

    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.assertj:assertj-core:+")
    testImplementation("org.testcontainers:junit-jupiter:+")
    testImplementation("org.testcontainers:selenium:1.20.+")
    testImplementation("org.testcontainers:mockserver:+")
    testImplementation("org.seleniumhq.selenium:selenium-chrome-driver:+")
    testImplementation("org.seleniumhq.selenium:selenium-support:+")
    testImplementation("org.mock-server:mockserver-client-java:5.15.+")
    testImplementation("io.rest-assured:rest-assured:+")
    testImplementation("org.springframework.boot:spring-boot:3.+") //to enable spring's default logback configuration (org/springframework/boot/logging/logback/defaults.xml and org/springframework/boot/logging/logback/console-appender.xml)
    testImplementation("org.springframework.boot:spring-boot-starter-logging:3.+") //to enable logback (with slf4j-api) with adapters (jcl-over-slf4j, jul-to-slf4j and log4j-over-slf4j)
    testImplementation("org.springframework:spring-jdbc:6.+")
    testImplementation("org.springframework:spring-web:6.+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")
}

val test by testing.suites.existing(JvmTestSuite::class)
tasks.register<Test>("e2eTest") {
    dependsOn(":portaro-runner:jib")
    group = "verification"
    description = "Runs integration tests."
    testClassesDirs = files(test.map { it.sources.output.classesDirs })
    classpath = files(test.map { it.sources.runtimeClasspath })
}

tasks.named<Test>("test") {
    enabled = false
}
