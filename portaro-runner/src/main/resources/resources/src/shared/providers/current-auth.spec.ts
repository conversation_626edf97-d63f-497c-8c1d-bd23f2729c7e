import type {Auth, User, UserKind} from 'typings/portaro.be.types';
import type CurrentAuthProvider from './current-auth.provider';
import * as angular from 'angular';
import providerModule from './providers.module';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
class UserMock implements User {
    public static mock = 'MOCK';
    addresses = [];
    schoolClass = null;
    authority = null;
    readableDepartments = [];
    contacts = [];
    email = null;
    evided = null;
    externalUserRole = null;
    rid = null;
    id = null;
    editorAccounts = [];
    readerRole = null;
    representedUsers = null;
    kind: UserKind.PERSON;
    role = null;
    text = null;
    userRoles = null;
    username = null;
    deleted = null;
    editableDepartments = [];
    readerAccounts = [];
    supplierAccounts = [];
    userServiceProperties = [];
}

class AuthMock implements Auth {
    activeUser = null;
    editableDepartments = null;
    readableDepartments = null;
    readableHierarchicalDepartments = null;
    evided = null;
    role = null;
}

const CURRENT_AUTH = new AuthMock();

describe('currentAuthProvider tests', () => {

    beforeEach(() => {
        window.currentAuth = CURRENT_AUTH;

        angular.mock.module(providerModule);
    });

    describe('config phase', () => {

        it('should inject currentAuthProvider to config phase', () => {
            angular.mock.module(/*@ngInject*/ (currentAuthProvider: CurrentAuthProvider) => {
                expect(currentAuthProvider).toBeDefined();
                expect(currentAuthProvider.getCurrentAuth()).toBe(CURRENT_AUTH);
            });
        });
    });

    describe('runtime phase', () => {

        let myCurrentAuth;

        beforeEach(() => {
            inject(/*@ngInject*/ (currentAuth) => {
                myCurrentAuth = currentAuth;
            });
        });

        it('should inject currentAuth', () => {
            expect(myCurrentAuth).toBeDefined();
            expect(myCurrentAuth).toBe(CURRENT_AUTH);
        });

    });

});
