<script lang="ts">
    import type {CssSize} from '../types';
    import {createEventDispatcher} from 'svelte';
    import {getLocalization} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import {fly} from 'svelte/transition';
    import {debounce} from 'lodash-es';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let searchQuery = '';
    export let minlength: number | null = null;
    export let placeholder: string | null = null;
    export let height: CssSize = '40px';
    export let searchIconSize: CssSize = '16px';
    export let resetIconSize: CssSize = '13px';
    export let width: CssSize = '360px';
    export let debounceMs = 300;
    export let enableReset = true;
    export let buttonColorAccented = true;
    export let forceResetButton = false;
    export let additionalClasses = '';

    const localize = getLocalization();
    const dispatch = createEventDispatcher<{'search': string, 'reset': void}>();
    const debouncedSearch = debounce(search, debounceMs);
    const resetButtonFlyAnimParams = {y: 3, duration: 250};
    $: isNotEmpty = exists(searchQuery) && searchQuery !== '';

    function search() {
        if (searchQuery.length === 0) {
            dispatch('reset');
            return;
        }

        if (exists(minlength) && searchQuery.length < minlength) {
            return;
        }

        dispatch('search', searchQuery);
    }

    const handleResetClick = () => {
        searchQuery = '';
        dispatch('reset');
    };
</script>

<form class="kp-search-bar {additionalClasses}"
      style="--search-bar-height: {height}; --search-bar-width: {width}; --search-icon-size: {searchIconSize}; --reset-icon-size: {resetIconSize};"
      role="search"
      on:submit|preventDefault={debouncedSearch}>

    <input class="search-input"
           type="search"
           {placeholder}
           {minlength}
           bind:value={searchQuery}>

    {#if (enableReset && isNotEmpty) || forceResetButton}
        <button class="reset-button"
                type="reset"
                on:click={handleResetClick}
                transition:fly={resetButtonFlyAnimParams}
                aria-label="{localize(/* @kp-localization commons.hledat */ 'commons.zrusit')}">

            <UIcon icon="cross-circle"/>
        </button>
    {/if}

    <button class="submit-button"
            class:btn-default={!buttonColorAccented}
            class:btn-accent-blue-new={buttonColorAccented}
            disabled="{!isNotEmpty}"
            aria-label="{localize(/* @kp-localization commons.hledat */ 'commons.hledat')}">

        <UIcon icon="search"/>
    </button>
</form>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .kp-search-bar {
        position: relative;
        height: var(--search-bar-height);
        display: flex;
        flex-shrink: 0;
        max-width: 100%;
        width: var(--search-bar-width);

        .search-input {
            border-radius: @border-radius-default 0 0 @border-radius-default;
            height: 100%;
            flex: 1;
            background-color: @themed-body-bg;
            border: 1px solid @themed-border-default;
            outline: none;
            overflow: hidden;
            white-space: nowrap;
            padding: 0 @spacing-m;
            transition: border-color 0.3s ease-in-out;

            &:active,
            &:focus {
                border-color: var(--accent-blue-new);
            }

            &:focus-visible {
                outline: none !important;
                box-shadow: none !important;
            }
        }

        .submit-button {
            border-radius: 0 @border-radius-default @border-radius-default 0;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            outline: none;
            border-left: none;
            cursor: pointer;
            height: 100%;
            font-size: var(--search-icon-size);
            width: calc(var(--search-bar-height) * 1.25);
            transition: opacity 0.3s ease-in-out;
        }

        .reset-button {
            color: @themed-text-default;
            background: none;
            outline: none;
            border: none;
            cursor: pointer;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: var(--reset-icon-size);
            right: calc(var(--search-bar-height) * 1.25 + 4px);

            &::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: calc(var(--reset-icon-size) * 1.75);
                height: calc(var(--reset-icon-size) * 1.75);
                transform: translate(-50%, -50%);
                border-radius: 50%;
                opacity: 0;
                transition: opacity 0.2s ease-in-out;
                background-color: rgba(18, 57, 255, 0.08);
            }

            &:hover::before {
                opacity: 1;
            }
        }
    }
</style>