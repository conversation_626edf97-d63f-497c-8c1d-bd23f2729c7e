import {exists} from 'shared/utils/custom-utils';

export interface HierarchicalFilterable {
    children: HierarchicalFilterable[];
    text: string;
    name: string;
}

/**
 * Filters hierarchical data structures while preserving the whole hierarchy when a match is found.
 * When a department matches the search query, the entire branch (parents and children) is included.
 *
 * @param items - Array of hierarchical items to filter
 * @param query - Search query string
 * @param matchFunction - Optional custom function to determine if an item matches the query
 * @returns Filtered array maintaining hierarchical structure
 */
export function filterHierarchicalData<T extends HierarchicalFilterable>(
    items: T[],
    query: string,
    matchFunction?: (item: T, searchQuery: string) => boolean
): T[] {
    if (!query.trim()) {
        return items;
    }

    const searchQuery = query.toLowerCase();

    const defaultMatchFunction = (item: T): boolean => {
        return item.text.toLowerCase().includes(searchQuery) ||
            item.name.toLowerCase().includes(searchQuery);
    };

    const itemMatches = matchFunction ?
        (item: T) => matchFunction(item, searchQuery) :
        defaultMatchFunction;

    const hasMatchInSubtree = (item: T): boolean => {
        if (itemMatches(item)) {
            return true;
        }
        return exists(item.children) && item.children.some((child) => hasMatchInSubtree(child as T));
    };

    const filterItem = (item: T): T | null => {
        const hasMatch = itemMatches(item);
        const hasChildMatch = exists(item.children) && item.children.some((child) => hasMatchInSubtree(child as T));

        if (!hasMatch && !hasChildMatch) {
            return null;
        }

        // If this item or any of its children match, include it with filtered children
        const filteredChildren = exists(item.children) ?
            item.children
                .map((child) => filterItem(child as T))
                .filter((child) => child !== null) :
            [];

        return {
            ...item,
            children: filteredChildren
        } as T;
    };

    return items
        .map((item) => filterItem(item))
        .filter((item) => item !== null);
}
