import register from '@kpsys/angularjs-register';
import filtersRoutes from './filters.routes';
import {KpFiltersPresenter} from './kp-filters.presenter';
import {FiltersDataService} from './filters.data-service';


export default register('portaro.features.filters-page')
    .config(filtersRoutes)
    .service(FiltersDataService.serviceName, FiltersDataService)
    .service(KpFiltersPresenter.presenterName, KpFiltersPresenter)
    .name();