<script lang="ts">
    import type {Filter} from 'typings/portaro.be.types';
    import KpMatcherItem from './KpMatcherItem.svelte';

    export let filter: Filter;
</script>

<div class="filter-item">
    <div>
        <strong>{filter.id} {filter.name ?? ''}</strong>
        <span>
            {#if filter?.matchResult?.allowed}
                <span class="label label-success">ALLOWING</span>
            {/if}
            {#if filter?.matchResult?.denied}
                <span class="label label-danger">DENYING</span>
            {/if}

            {#if filter?.matchResult && !filter?.matchResult?.allowed && !filter?.matchResult?.denied }
                 <span class="label label-default">SKIPPING TO NEXT</span>
            {/if}
        </span>
        {#if filter.description}
            <span>{filter.description}</span>
        {/if}
    </div>

    {#if filter.matcher}
        <KpMatcherItem matcher={filter.matcher}/>
    {/if}

    {#if filter.filters}
        {#each filter.filters as nestedFilter (nestedFilter.id)}
            <svelte:self filter={nestedFilter}/>
        {/each}
    {/if}
</div>

<style>
    .filter-item {
        margin-left: 40px;
        padding-top: 5px;
    }
</style>