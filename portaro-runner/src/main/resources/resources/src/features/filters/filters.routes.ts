import type {StateProvider} from '@uirouter/angularjs';
import {KpSvelteComponentWrapperComponent} from 'core/kp-svelte-component-wrapper/kp-svelte-component-wrapper.component';

/*@ngInject*/
export default function filtersRoutes($stateProvider: StateProvider) {
    let filtersModule: { default: any; };
    $stateProvider
        .state({
            name: 'filters',
            url: '/filters',
            component:  KpSvelteComponentWrapperComponent.componentName,
            resolve: {
                component: () => filtersModule.default,
            },
            lazyLoad: async () => {
                filtersModule = await import(/* webpackChunkName: "filters" */ './KpFilters.svelte');
                return null;
            }
        });
}