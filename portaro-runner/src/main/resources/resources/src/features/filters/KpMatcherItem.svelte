<script lang="ts">
    import type {Matcher} from 'typings/portaro.be.types';

    export let matcher: Matcher;
</script>

<div class="matcher-item">
    <div>
        {#if matcher.name}
            <strong>{matcher.name}</strong>
        {/if}
        {#if matcher.description}
            <span>{matcher.description}</span>
        {/if}
    </div>

    {#if matcher.sourceData}
        <div class="matcher-item-source-data">
            <div>ID: {matcher.sourceData.id}</div>
            <div>Type: {matcher.sourceData.type ?? ''}</div>
            <div>Negated: {matcher.sourceData.negated}</div>
            <div>Value: {matcher.sourceData.value ?? ''}</div>
            <div>Description: {matcher.sourceData.description ?? ''}</div>
        </div>
    {/if}

    {#if matcher.matchers}
        {#each matcher.matchers as nestedMatcher}
            <svelte:self matcher={nestedMatcher}/>
        {/each}
    {/if}

    {#if matcher.target}
        <svelte:self matcher={matcher.target}/>
    {/if}
</div>


<style>
    .matcher-item {
        margin-left: 40px;
        padding-top: 5px;
    }

    .matcher-item-source-data {
        font-size: 0.6em;
    }

</style>