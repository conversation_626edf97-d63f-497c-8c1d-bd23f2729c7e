import type {FiltersDataService} from './filters.data-service';
import type {Filter} from 'typings/portaro.be.types';

export class KpFiltersPresenter {
    public static presenterName = 'kpFiltersPresenter';

    /*@ngInject*/
    constructor(private filtersDataService: FiltersDataService) {
    }

    public async getFilters(): Promise<Filter[]> {
        return this.filtersDataService.getAll();
    }


}