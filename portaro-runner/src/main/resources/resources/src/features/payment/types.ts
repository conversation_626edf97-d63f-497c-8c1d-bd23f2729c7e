import type {BasicUser, Department, FormPackage, Identified, LabeledIdentified, NamedLabeledIdentified, Ordered, StaticLink} from 'typings/portaro.be.types';

export type PayRequest = {
    provider: string;
    payer: BasicUser;
    cashierDepartment: Department;
    amounts?: ContraDebtablePaymentItemRequest[];
    itemsSpec?: PaymentItemsSpecRequest;
}

export interface ContraDebtablePaymentItemRequest {
    owner: BasicUser;
    sum: number;
    type: AmountType;
    withContraDebt: boolean;
    enabled: boolean;
    direct: boolean;
}

export interface ViewablePaymentRequestItem extends ContraDebtablePaymentItemRequest {
    maxSum: number;
}

export type AmountTypeCategory = LabeledIdentified<number>;

export interface AmountType extends NamedLabeledIdentified<number>, Ordered {
    creditable: boolean;
    category: AmountTypeCategory;
    forDebtPaymentsOnly: boolean;
}

export interface PaymentItemsSpecRequest {
    owner?: BasicUser[];
    directPaymentOwner?: BasicUser;
    amountType?: AmountType[];
}

export interface PayRequestFormPackage extends FormPackage<PayRequest> {
    responseType: string;
    text: string;
    finished: boolean;
    directPaymentAddable: boolean;
    typesEditable: boolean;
    sumsEditable: boolean;
    submitMethod: string;
    submitAsPage: boolean;
    submitPath: string;
}

export interface Amount {
    sum: number;
}

export interface TypedAmount extends Amount {
    type: AmountType;
}

export interface TypedUserAmount extends TypedAmount {
    owner: BasicUser;
}

export interface CreditTransfer {
    owner: BasicUser;
    targetType: AmountType;
    sourceTypes: AmountType[];
}

export interface PayCommand {
    provider: string;
    payer: BasicUser;
    cashier?: BasicUser | null;
    department: Department;
    items: AmountGroup<TypedUserAmount>;
    sumToPay: number;
}

interface AmountGroup<E extends Amount> extends Amount {
    transactions: E[];
}

export interface Payment extends Identified<number> {
    department: Department;
    state: PaymentState;
    request: PayCommand;
    provider: string;
    payer: BasicUser;
    cashier?: BasicUser | null;
    sumToPay: number;
    items: TypedUserAmount[];
    createDate: string;
    cancelDate: string;
    payDate: string;
    refundDate: string;
    timeoutDate: string;
    error?: string;
}

export enum PaymentStateId {
    CREATED = 1,
    CANCELED = 2,
    TIMEOUTED = 3,
    PAID = 4,
    REFUNDED = 5,
    PARTIALLY_REFUNDED = 6
}

export interface PaymentState extends LabeledIdentified<PaymentStateId> {
    previous?: PaymentState;
}

export interface PaymentGatewayRedirectModalModel {
    debts: TypedUserAmount[];
    redirectUrl: string;
    infoText: string;
    termsAndConditionsLink?: StaticLink;
    text: string;
    sumToPay: number;
}