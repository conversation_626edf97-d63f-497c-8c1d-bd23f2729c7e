# Changelog


## 2025-08-01
Klient - <PERSON><PERSON>idali jsme dvě nové funkce, kter<PERSON> usnadňují hlášení chyb a pomáhají nám s jejich rychlejším řešením.

**Hlášení o chybě**: Nyní můžete vygenerovat a stáhnout soubor s výpisem chyb a systémovými informacemi.
Hlášení vygenerujete kliknutím na odkaz v patičce nebo klávesovou zkratkou `Ctrl+Alt+D` (`Control+Option+D` na macOS).

**Screenshot (snímek obrazovky)**: Nově můžete vytvořit a stáhnout snímek obrazovky ve formátu PNG.
Screenshot vytvoříte klávesovou zkratkou `Ctrl+Alt+S` (`Control+Option+S` na macOS).
Webový prohlížeč pak uživatele vyzve k potvrzení sd<PERSON><PERSON>, což je nutné k vytvoření screenshotu.
<PERSON><PERSON> funkce je dostupná pouze v desktopových (ne v mobilních) verzích moderních prohlížečů.

V případě problémů můžete vytvořit screenshot a hlášení o chybě, které pak přiložíte do připomínky v připomínkovém systému.

## 2025-07-31
Klient - Zavedena detekce zastaralých a nepodporovaných webových prohlížečů. 
Pokud uživatel používá takový prohlížeč, systém ho na to upozorní.

## 2025-07-30
SIP2 - Přidání podpory pro potvrzovací dialog s upozorněním, že čtenár měl titul již v minulosti vypůjčen.
Funkce se zapíná v ini `sip2.lending.lentInPastFVConfirmationEnabled` a je třeba, aby klient podporoval SIP2 `FV` field. (viz např. https://help.wise.oclc.org/Staff_client/Library_self-service/SIP2_for_Wise/Description_dialogs_for_SIP2#SIP2.2B) 

## 2025-05-28
Úpravy uživatelského rozhraní
- Systém modálních oken prošel funkčním a vizuálním vylepšením. 
- Odstraněna Javascriptová část softwarové knihovny Bootstrap 3.
- Příprava na odstranění zastaralé softwarové knihovny jQuery. K úplnému odstranění dojde **31. 7. 2025**. 
Po tomto datu už nebude systém automaticky podporovat jQuery skripty v konfiguračních (custom) souborech a existující skripty přestanou fungovat.
Knihovnám, které používají jQuery, zajistíme migraci na aktuální verzi jQuery individuálně.

## 2025-04-15
https://support.kpsys.cz/issues/21896
Odstranění podpory pro starý protokol pro přihlášení přes MojeID. Kterému skončila podpora na konci března 2025.

## 2025-03-07
https://support.kpsys.cz/issues/22629
Drobné vizuální opravy a vylepšení kolekcí včetně aktualizace ikonek.

## 2025-01-30
Nová logika vytváření uživatelů a prodloužení registrace
    - poté co je nový uživatel vytvořen a nemá nenulové registrační poplatky není mu automaticky přiřazen začátek a konec registrace a také mu není vygenerován dluh
    - registrace se mu vytvoří až když si sám klikne na tlačítko dokončení registrace
    - v nastavení přibylo nové nastavení forcePaymentBeforeExtendingRegistrationPeriod které umožnuje vynutit zaplacení registračního poplatku před prodloužením registrace

## 2024-12-13
https://support.kpsys.cz/issues/22302
Zobrazitelné (tzn. vyhledatelné) fondy se nyní vždy rozlišují na základě aktuálního uživatele.
 - Pokud je aktuální uživatel nepřihlášen nebo je přihlášen čtenář (resp. ne knihovník), zobrazitelné fondy jsou ty z ini `OPAC.FondyDokumentu` a `OPAC.FondyAutorit`,
 - Pokud je přihlášen knihovník s editačními právy na aktuálním oddělení, zobrazitelné fondy jsou všechny povolené (ve Verbisu)

## 2024-12-05
Ukončení podpory pro url adresy přesměrující na stránku s detailem dokumentu `/documents?detail_num=kpwXXXXX` a `/zaznam.php?detail_num=kpwXXXXX`.
Tyto adresy byly používány ještě v dobách PHP verze OPACu, nyní jsou již definitivně odstraněny.

## 2024-11-26
https://support.kpsys.cz/issues/21896
Přídání podpory pro nový protokol pro přihlášení přes MojeID. Tento nový protokol by mě nahradit stávající protokol postavený na OpenID 2.0 který bude končit koncem března 2025.

Po krátkou dobu budou podporovány oba protokoly.

## 2024-11-26
https://support.kpsys.cz/issues/22178
Změněn způsob selektování polí a podpolí ve velocity skriptech (tzn. v uživatelsky definovatelných odstavcích, např. v ini `OPAC_RECORD.TemplateDocumentDetailUnderTitle` nebo v konfiguračních souborech).

Dříve byl formát 'D.856.a' nebo 'A.100.a' (D/A podle toho, zda je to pole u dokumentu nebo autority). Nyní je formát 'd856.a' nebo 'a100.a'.

Tedy z:
```
#sf($record 264 'abc')
#sfRaw($record 264 'abc')
#fg($record.query('264.abc') 'Publikace: ')
$record.getSubfield(264, 0, 'a', 0) $record.getSubfield(264, 0, 'b', 0) $record.getSubfield(264, 0, 'c', 0)
```

se formát mění na:
```
#sf($record 'd264' 'abc')
#sfRaw($record 'd264' 'abc')
#fg($record.query('d264.abc') 'Publikace: ')
$record.getSubfield('d264', 0, 'a', 0) $record.getSubfield('d264', 0, 'b', 0) $record.getSubfield('d264', 0, 'c', 0)
```

Po krátkou dobu budou podporovány obě varianty, skripty budou ale v některých z následujících aktualizací automaticky přemigrovány na nový formát.


## 2024-11-06
https://support.kpsys.cz/issues/22079
Přidána podpora pro "dědění" fondů, kdy každý fond nyní může rozšiřovat jiný v tom smyslu, že bude obsahovat (kromě svých) i všechna jeho pole.
Tedy, je možné vytvořit obecný fond "dokument", který bude mít pole 245 a následně fond "monografie", který bude "dokument" rozšiřovat, tedy bude mít (kromě svých "mongrafických" polí, napr. pole 20) také pole 245. A fond "perodikum", které bude pak také obsahovat pole 245 z "dokumentu" a své pole 22 (ISSN). 
Pak je možné na tento obecný fond odkazovat v jiných záznamech, které díky tomu mohou mít vazbu na jakýkoliv dokument, nikoliv jen na monografii nebo periodikum

Na základě toho se mění název vyhledávacích parametrů:
- `kind`, pokud se používá s hodnotou `document` nebo `authority`, se mění na `subkind`
- `fond` se mění na `rootFond`

Pokud tedy knihovna používá odkazy na vyhledávání mimo everbis/portaro, je třeba parametry v odkazu upravit. Odkazy přímo v everbisu/portaru se aktualizují automaticky.

## 2024-11-04
https://support.kpsys.cz/issues/21876
Kompletní redesign editace záznamu

## 2024-10-10
https://support.kpsys.cz/issues/19015
Implementace BankID

## 2024-09-08
https://support.kpsys.cz/issues/21054
Vydána nová verze (2.0) modulu MediaViewer (VerbisViewer), která obsahuje spousty nových funkcí, nový design, podporu mobilních telefonů a tabletů, různé optimalizace pro zvýšení výkonu a v neposlední řadě více podporovaných typů souborů.
Odstraněna stará verze MediaViewer 1.0 a všechen kód s ní spojený.

## 2024-08-28
https://support.kpsys.cz/issues/21669
Nový editor telefonních čísel, který i validuje, zda telefonní existuje (resp. má validní předvolbu a předvolbu operátora)

## 2024-05-15
https://support.kpsys.cz/issues/21196
Upraveno načítání nejnovějších dokumentů (např. ve slideru novinek):
Pokud knihovna používá upravený db view `VIEW_OPAC_NEWS`, je třeba vytvořit nový, upravený s vlastním názvem (např. `kfbz_view_record_news`),
který musí vracet sloupce record_id, publish_date, department_id, a exemplar_status_id. Tzn. analogicky jako dříve, ale s `record_id` místo `fk_zaz` apod.
Následně je třeba nastavit, aby portaro tento pohled používalo, a to do ini `record.search.newest.viewName`.

## 2024-05-02
https://support.kpsys.cz/issues/20268
Implementováno základní rozhraní Získej, jak pro pasivní, tak pro aktivní MVS. Pro zprovoznění prosím kontaktujte společnost KP-sys.

## 2024-04-26
https://support.kpsys.cz/issues/21099
Sjednocení MVS žádanek a samotných MVSek, kdy si čtenáíři knížku, o kterou mají přes MVS zájem, mohou sami vyhledat (např. v Z-serveru)

## 2024-02-07
https://support.kpsys.cz/issues/20705
Konto čtenáře - Přídána možnost tisknout **Potvrzení o výpůjčce**. Toto lze vypnout v INI - `OPAC_USER.ShowUserLoanConfirmationPrintButton`.

## 2024-01-11
OAI - Opraven příznak smazaného záznamu v OAI XML: původně byl chybně jako samostatný element `status`, tedy `<status>deleted</status>`, nyní je správně jako atribut elementu `header`, tedy `<header status="deleted">`:
```xml
<header>
    <identifier>oai:kpsys.cz:362ba3cd-3ce5-40b4-adfc-31a358733480</identifier>
    <datestamp>2021-10-20</datestamp>
    <status>deleted</status>
    <setSpec>default</setSpec>
</header>
```
nyní
```xml
<header status="deleted">
    <identifier>oai:kpsys.cz:362ba3cd-3ce5-40b4-adfc-31a358733480</identifier>
    <datestamp>2021-10-20</datestamp>
    <setSpec>default</setSpec>
</header>
```

## 2024-01-04
Systém - Windows verze portara nyní běží na Javě 21 (dříve 17). Aktualizace javy probíhá automaticky. Linuxové (docker) verze běží aktuálně na Javě 20.

## 2023-12-19
https://support.kpsys.cz/issues/20542
Rapidní zvýšení rychlosti načítání katalogu díky přidání komprese statických souborů a díky HTTP cachování

## 2023-12-01
Portaro nově umožňuje mazat všechny typy uživatelů, nejen čtenáře, jak to bylo do teď.
Formulář pro editaci uživatele nově umožnuje mazat čtenářské a knihovnické údaje.
Pro odebrání role knihovníka nebo čtenáře jednoduše smažte příslušné údaje v editačním formuláři.

## 2023-11-30
https://support.kpsys.cz/issues/20268
Implementováno vytváření pasivních MVS skrze nový MVS modul (menu `Výpůjčky` -> `MVS objednávky` -> `Vytvořit novou objednávku`).
Zatím v "preview" verzi, nicméně vytváření je již funkční. Vyvořeno v rámci implementace napojení na službu Získej.

## 2023-11-19
Prejmenování maker `#sfSep` -> `#sf`, `#sfRawSep` -> `#sfRaw`, `#fondSep` -> `#fond`. Všechna nastavení jsou na nový formát přemigrována automaticky.

## 2023-11-15
https://support.kpsys.cz/issues/17088
Editace - Editace autoritniho pole 008 se nově provádí pomocí validačního formuláře jako ve Verbisu.

## 2023-11-10
Přidána podpora pro hledání dalších typů uživatelů (čtenáři, knihovníci, ...). Nově lze také hledat typy: `rodina`, `firma` a `software`.

## 2023-11-03
https://support.kpsys.cz/issues/20390
eVýpůjčky - Přidána podpora pro audioknihy a PDF knihy poskytovatele evýpůjček Palmknihy.cz.

## 2023-10-28
Portaro nově umožňuje vytvářet, upravovat a mazat půjčovny. V menu `Nastavení` -> `Půjčovny`.

## 2023-09-12
https://support.kpsys.cz/issues/19891
Design - nahrazení původního fontu novým fontem `Ubuntu`.

## 2023-07-10
https://support.kpsys.cz/issues/19981
Vyvinuto nové HTTP JSON API pro možnosti hromadných úprav jednoho či více polí v jednom či více záznamech.
Dokumentace je [zde](/docs/apidocs/index.html#_record_bulk_edit_resource)

## 2023-07-10
https://support.kpsys.cz/issues/19132
SIP2 - Přidána podpora pro automatické prodlužování všech výpůjček v rámci každého příkazu "Patron Information" (příkaz číslo 63)

## 2023-07-04
https://support.kpsys.cz/issues/19933
Tlačítko pro vypůjčení má nyní třetí variantu - kromě červeného (značícího nedostupnost) a zeleného (značícího jakkoliv dostupný) nyní podporuje i třetí možnost, oranžovou, která značí, že titul lze pouze rezervovat (čtenář na něj musí počkat).

## 2023-07-03
https://support.kpsys.cz/issues/19891
Portaro nyní podporuje jednodušší nastavení barevného ladění katalogu - Pokud knihovna nepotřebuje komplexnější úpravy designu přes CSS, lze nastavit pouze barvy v ini sekci `gui.theming`.

## 2023-06-22
https://support.kpsys.cz/issues/19224
Url adresy při vyhledávání jsou nyní mnohem kratší - obsahují nyní pouze nejnutnější vyhledávací parametry

## 2023-06-20
Vydána verze Portara 2.3, obsahující zejména nový design tlačítek u vyhledaných záznamů, které má několik výhod:
 - je oproti původnímu řešení přístupné,
 - je oproti původnímu řešení použitelné i na mobilních telefonech. Původní řešení na mobilu zabíralo více jak polovinu obrazovky, takže se text u vyhledaného záznamu musel za každým slovem zalamovat a byl tak těžko čitelný.
 - Dostupnost záznamu je u nového řešení vidět (ve formě ikonky) bez nutnosti myší najíždět na vyhledaný záznam.

## 2023-06-05
https://support.kpsys.cz/issues/19658
Funkce "Zapomenuté heslo" nyní podporuje situaci, kdy má stejný email více uživatelu a situaci, kdy chceme vyresetovat heslo dítěte jeho zákonným zástupcem

## 2023-05-29
https://support.kpsys.cz/issues/17612
web api - Odstraněny již zastaralé endpointy na nastavování statusů `/record-holdings/{holdingId}/finish-cataloging`, `/record-holdings/{holdingId}/unfinish-cataloging` a `/record-holdings/{holdingId}/incomplete`. Ty byly již dříve nahrazeny `/records/{recordId}/phase` přímo na úrovni záznamu.

## 2023-05-29
Migrace unikátních identifikátorů záznamu (uuid) z pole `record.recordUuid` do `record.id`. Původní hodnota (číselná) hodnota se z `record.id` přesunula do `record.kindedId`. Pokud vtl šablony obsahují takové struktury, je třeba je ručně opravit/vyřešit.

## 2023-05-22
Licence - Portaro nyní u licence ověřuje url adresu, pro kterou je vystaven. V případě, že nesouhlasí s tou, která je nastavena v OPAC.URL, hlási upozornění v horní liště katalogu. Je možné, že se v budoucnu bude chovat striktněji, tedy, že systém nebude možné spustit.

## 2023-04-20
https://support.kpsys.cz/issues/19617
Výpůjčky - Přidána množnost zobrazovat informace o Verbisboxu a možnost generovat přístupový PIN z Portara.
Pokud je Verbisbox aktivovaný, tak tuto možnost najdete v menu: `Výpůčky` > `Verbisbox`.
Zatím se jedná jen o prvotní verzi a další funkční i vizuální změny přijdou brzy.

## 2023-04-02
https://support.kpsys.cz/issues/19592
Editace - Přidáno nastavení výchozích hodnot polí při editaci dokumentů a autorit

## 2023-03-23
https://support.kpsys.cz/issues/17879
Nový systém detekce botů a automatizovaných webových přístupů - pro přesnější statistiky přístupů do katalogu

## 2023-03-06
https://support.kpsys.cz/issues/18638
Rapidní zrychlení (o 50-100ms) zobrazení souborů (obálek apod.) - zatím zapnuto pouze v některých databázích. Manuálně lze zapnout v ini `file.httpclient5Enabled`

https://support.kpsys.cz/issues/19509
Zobrazení článků a částí dokumentu v detailu záznamu je nyní stránkované (=> lepší uživatelská přívětivost)

## 2023-02-27
https://support.kpsys.cz/issues/18866
Editace - Při kopírování záznamů se nyní ne zachovává pole 001 - vždy se vygenerovává nové

## 2023-02-22
https://support.kpsys.cz/issues/18489
Editace - Přidáno slučování autorit, stejným způsobem, jako je slučování dokumentů, tedy označením zdrojových záznamů a kliknutím na "sloučit" na cílovém záznamu

## 2023-02-20
https://support.kpsys.cz/issues/19159
Editace - Systém nyní umožňuje kopírování záznamů i knihovníkům, kteří nemají práva k vyvoření záznamu ve stejném fondu. Nyní se uživateli nabídne výběr fondů, na které práva má.

## 2023-02-04
https://support.kpsys.cz/issues/19369
SIP2 - CheckOut (vypůjčení) je nyní striktnější - kontroluje překročený max. počet výpůjček a zakazuje půjčení více exemplářů stejné knihy

## 2023-02-02
https://support.kpsys.cz/issues/19317
Změna "do data" ve statistikách - nyní je datum exkluzivní, nikoliv inkluzivní. To řeší problémy s časobvými zónami, které v některých případech způsobovaly vyjmutí roku o poslední den. Je možné, že toto opatření je pouze dočasné, pokud se tento problém podaří vyřešit na frontendu (tzn. přímo u editovaného datumového pole).

## 2023-02-02
https://support.kpsys.cz/issues/19317
Sjednocení formátu data pro JSON API a url parametry napříč celým portarem na standardní ISO formát (např '2011-12-03T10:15:30Z').

## 2023-01-31
https://support.kpsys.cz/issues/19041
Intervalové výpůjčky - přidána podpora pro možnost nastavení intervalů pomocí nadefinování různých období (zkouškové, semestr, prázdniny, svátky) a jejich přiřazení pro jednotlivé intervaly. Je tak nově možné nastavit jiné intervaly pro např. zkouškové období a jiné pro semestr. Nebo různé intervaly pro různé dny v týdnu.

## 2023-01-16
SIP2 - Rozdělení výpisu výpůjček v operaci 64 na "unavailable hold items", jakožto výpůjčky čekající (nepřipravené nebo ve frontě) a na "hold items", jakožto výpůjčky připravené k vyzvednutí. Jedná se o úpravu pro kompatibilitu s cosmotron selfcheckem a je možné, že jiné selfchecky pracují jinak - v takovém případě budeme chování ještě měnit.

## 2022-11-16
https://support.kpsys.cz/issues/18783
SIP2 - Upraveno nastavení rozlišování klientů - nyní se pole `AC` (*terminal password*) použije pro načtení *oddělení terminálu* (tzn. oddělení na kterém se budou provádět výpůjčky a vracení) podle nastavení `sip2.terminalPassword`. Nové ini `sip2.context` pak nastavuje, pro které *oddělení terminálu* náleží tzv. *kontextové oddělení*. *Kontextové oddělení* definuje, které všechny výpůjčky, čtenáři apod. jsou na daném terminálu vidět.
Tedy, pokud mám terminál v Malenovicích, který posílá v `AC` hodnotu `selfcheck-malen`, vytvořím nové oddělení pod Malenovicemi, např "Malenovice Selfcheck". Do `sip2.terminalPassword` nastavím hodnotu `selfcheck-malen` na oddělení "Malenovice Selfcheck". Pokud chci, aby terminál zobrazoval výpůjčky a čtenáře v rámci celých Malenovic, do `sip2.context` nastavím pro "Malenovice Selfcheck" hodnotu "Malenovice".
Toto nastavení je dočasně kombinováno s nastavením `sip2.lendingDepartment`, to bude však v blízké době smazáno.

## 2022-11-11
https://support.kpsys.cz/issues/18910
SIP2 - Přidána možnost nastavení maximálního dluhu, který ještě povoluje půjčování přes SIP2. Nastavením `sip2.lending.debtLimit`, popř. specifikováním nezahrnovaných typů poplatků `sip2.lending.debtLimitExcludedAmountTypes`.

## 2022-10-24
https://support.kpsys.cz/issues/18867
SIP2 - Seznam načítaných výpůjček čtenáře nyní koresponduje se seznamem v Portaru (tzn. načítají se výpůjčky ze stejné hierarchie oddělení)

## 2022-10-21
https://support.kpsys.cz/issues/18587
Pole "stát" v adresách uživatelů je změněno na číselník, aby do něj nebylo možné zapisovat nesmysly. Data v knihovnách jsou při startu portara nebo aplikačního serveru automaticky přemigrována a částečně vyčištěna (např "Česká Republika", "česko", "česká rep." jsou přemigrována do CZE).

## 2022-10-17
https://support.kpsys.cz/issues/14692
AUTH - Přidána možnost přihlašování do Portara přes Google a Facebook. Pro nastavení prosím kontaktujte KPSYS.

## 2022-10-05
https://support.kpsys.cz/issues/18783
SIP2 - Přidána podpora pro rozlišování SIP2 klientů (selfchecků apod.). To je řešeno pomocí SIP2 fieldu `AC` neboli *terminal password*. Pokud je v ini SIP2.terminalPassword nastavena hodnota, která se shoduje s `AC` v SIP2 requestu, použije se department pro dané ini. Neboli, pokud selfcheck pošle v `AC` hodnotu `selfcheck-malen` a v ini je pro oddělení Malenovice také hodnota `selfcheck-malen`, pak je operace prováděna v rámci Malenovic.

## 2022-09-20
https://support.kpsys.cz/issues/18708
OAI - Opraveno zpracování parametrů `from` a `until`, kdy se použilo nesprávné časové pásmo (nyní UTC, podle specifikace) a v důsledku se přeskočilo hledání záznamů o den dříve. Dále je opraveno zjišťování smazaných záznamů na jednotlivých departmentech.

## 2022-08-24
https://support.kpsys.cz/issues/16020
OAI - Exportované exempláře (v marcovských polí záznamů) nyní respektují stejné nastavení, jako má vyhledávání exportovaných záznamů. Tzn. pokud v OAI setu je nastaveno omezení některých budov, exempláře z těchto budov se nebudou exportovat ani do záznamů, které mají exempláře i v povolených budovách

## 2022-08-19
https://support.kpsys.cz/issues/18575
WEB - Rozsáhlé vylepšení přístupnosti Portara (zejména podpora čteček pro nevidomé)

## 2022-08-15
https://support.kpsys.cz/issues/18570
SDI - Rozsáhlá optimalizace (= vyřešeny problémy s výkonem)

## 2022-08-05
https://support.kpsys.cz/issues/17484
SYNC - Přidána podpora pro synchronizaci uživatelů ze školního systému Škola OnLine

## 2022-06-24
https://support.kpsys.cz/issues/17292
SIP2 - Migrace uživatelsky definovaných DB procedur `SPROC_SELFCHECK_PRIHL_CTEN` a `SPROC_SELFCHECK_PUJC_EXEMP` do ini, v DB zůstává pouze `SPROC_SELFCHECK_VRAT_KOS`

## 2022-05-26
https://support.kpsys.cz/issues/18253
Přidáno zobrazování náhledů i pro PDF soubory

## 2022-05-05
https://support.kpsys.cz/issues/16663
Načítání autoritních rejstříků zrychleno cca 10x

## 2022-04-27
https://support.kpsys.cz/issues/17126
OAI, NCIP - Načítání exemplářů podle interního ID, nikoliv podle čárových kódů, které nemusejí být v knihovnách vždy používány

## 2022-04-26
https://support.kpsys.cz/issues/18033
EMAIL - Nastavení emailů (SMTP serveru) v portaru je nyní plně dynamické, tzn. nevyžaduje restart portara

## 2022-03-07
https://support.kpsys.cz/issues/17391
OAI - Další rapidní zrychlení načítání OAI záznamů pomocí tzv. "keyset pagination". Dříve se s narůstajícími stránkami stahovaných záznamů stahování postupně zpomalovalo, kvůli známému problému u databází, tzv. offset pagination. Načtení velkého množství záznamů (např. celé databáze) pak bylo velmi pomalé a řešilo se např. speciálními sety `cpkinit`. Tzv. "keyset pagination" tento problém řeší, rychlost stahování je nynív postatě konstantní.

## 2022-03-04
https://support.kpsys.cz/issues/17747
Kolekce záznamů - veřejné kolekce jsou nyní viditelné i pro nepřihlášené čtenáře.

## 2022-03-02
https://support.kpsys.cz/issues/17391
OAI - Z OAI odpovědí je nyní vynecháno načítání celkového počtu záznamů (parametr "completeListSize"), díky čemuž je načítání velkých souborů několikanásobně rychlejší, hlavně na začátku importu.

## 2022-03-02
https://support.kpsys.cz/issues/17391
OAI - Přepracováno načítání záznamů tak, aby bylo možné jednodušeji definovat, které záznamy budou exportovány například i v závislosti na tom, zda mají exempláře na daném oddělení.
Je pak možné sjednotit exemplářová pravidla, která se použijí při vyhledávání OAI záznamů s pravidly, která se použijí při vyhledávání exemplářů v obsazích záznamů.
Dříve byly OAI sety definovány přímo SQL dotazem, nyní jsou definovány sadami omezujících pravidel, například pro fondy nebo pro to, zda má záznam exempláře či nikoliv.

## 2022-02-22
https://support.kpsys.cz/issues/17737
OAI - přidána možnost specifikace OAI setu nikoliv ve standardním url parametru `set`, ale v cestě. Tedy, například pro set `cpk` lze místo `/api/oai?verb=ListRecords&set=cpk...` použít `/api/oai/cpk&verb=ListRecords...`.

Tento způsob má velkou výhodu v tom, že lze set specifikovat i v případě stahování jednoho záznamu (`verb=GetRecord`), ve kterém se parametr `set` nepoužívá. Jinými slovy: pokud má knihovna pro různé sety (typicky pro set cpk) jiné nastavení exportovaných polí, v klasickém způsobu nelze u `verb=GetRecord` toto jiné nastavení použít (systém neví, že se jedná o set cpk). Použití `/api/oai/cpk&verb=GetRecord...` to umožňuje.

## 2022-02-08
https://support.kpsys.cz/issues/17502
Auth - přidána podpora pro doufaktorovou autentizaci. Druhým faktorem je autentizace posláním bezpečnostního kódu na email uživatele, který se následně zadá do formuláře v Portaru. Funkcionalita se zapíná v ini `AUTH.SecondFactorEnabled` (tím se aktivuje obecně 2FA) a `AUTH_EMAIL.Enabled` (tím se aktivuje autentizace přes email).

## 2022-01-27
https://support.kpsys.cz/issues/17401
Kolekce záznamů - Přidána možnost vytvářet kolekce záznamů podobně jako ve Verbisu.
- Kolekce jsou přístupné v hlavním menu: `Zajímavé tituly` > `Kolekce`
- Knihovník může vytvářet, editovat a mazat kolekce. Přihlášený čtenář může jen prohlížet.
- Aby byla kolekce viditelná pro ostatní knihovníky a čtenáře, musí mít nastavenou veřejnou kategorii.
- Kliknutím na jméno kolekce se otevře stránka s jejím obsahem.
- Pridávání záznamů do kolekce se provádí na stránce hledání. Vyhledané tituly se označí a po kliknutí na tlačítko `Přidat vybrané do kolekce` v panelu `Označené` se zobrazí modální dialog s výběrem kolekce. 

## 2022-01-25
https://support.kpsys.cz/issues/17612
Katalogizace - Status záznamu rozdělen na dvě části:
 - Status zpracování záznamu, který představuje fázi zpracování, tedy například "dokončená katalogizace" nebo "odesláno do CASLIN". Ten je nyní nezávislý na jakém oddělení je záznam zpracováván. Na všech oddelění (tedy např. i v rámci celého regionu) má záznam tento status stejný.
 - "Holdingový" status, který může být na každém oddělení jiný a definuje, zda je na něm záznam aktivní, vyřazený nebo smazaný.

## 2022-01-18
https://support.kpsys.cz/issues/17412
Placení - přidána podpora pro "virtuální platební terminál", který slouží pro knihovníky eVerbisu, kteří mají fyzický platebná terminál. Čtenář zaplatí kartou přes terminál a knihovník manuálně zadá platbu do konta čtenáře. Funkce se nastavuje přes ini `PAYMENT_TERMINAL_VIRTUAL.Enabled`.

## 2022-01-11
https://support.kpsys.cz/issues/17391
OAI - Úplné odstranění starých OAI setů `0`, `1`, `2`, `3`, které jsou již několik let nahrazeny sety `default`, `knih`, `monografie`, `seri`.
OAI - Smazána výchozí hodnota u ini `OAI_PROVIDER.VzorIdDokumentu`, aby bylo nutné ji vždy knihovnou explicitně nastavit (některé knihovny měly nastavenou výchozí hodnotu, což může být problém). Hodnota se obvykle nastavuje jako `oai:<doména knihovny>:{recordId}`, kde `<doména knihovny>` je např. `kfbz.cz`.

## 2022-01-10
https://support.kpsys.cz/issues/17502
Auth - Přidána možnost přihlašování přes emailem zaslaný odkaz. Zatím v BETA verzi.

## 2021-12-21
Systém - Přepracování a optimalizace načítání záznamů. Hledání a zobrazení dokumentů a autorit je nyní rychlejší.

## 2021-12-17
https://support.kpsys.cz/issues/17473
Exempláře - přidána možnost zobrazení pouze zúbytkovaných exemplářů (v menu "exempláře") tím, že se odznačí "Včetně nevyřazených" a naopak označí "Včetně vyřazených".

## 2021-12-16
https://support.kpsys.cz/issues/16817
Platby - Přidána integrace s online platební bránou od ČSOB

## 2021-12-10
https://support.kpsys.cz/issues/17391
- NCIP - Exempláře se nyní exportují (přes NCIP a pokud je zapnuto `OPAC_EXEMP.PortaroGeneratedExportedExemplars`, tak i přes záznamy v OAI) včetně těch, které leží na nadřazených odděleních aktuálního. Tedy, pokud má knihovna exemplář na vesnice.knihovna.cz i na region.knihovna.cz, kde vesnice patří pod region, tak při exportu na vesnice.knihovna.cz systém vyexportuje exempláře jak z vesnice, tak z oddělení region.
- OAI - Pokud je zapnuto ini `OPAC_EXEMP.PortaroGeneratedExportedExemplars`, exempláře se do exportovaných záznamů generují portarem, nikoliv aplikačním serverem. Navíc s tím, že se exportují pouze exempláře ležící v nadřazených odděleních a v odděleních, které jsou v podstromu oddělení, na kterém se OAI volá.

## 2021-12-09
https://support.kpsys.cz/issues/17391
OAI - Přepracování jádra OAI provideru. Kvůli tomu bude již zakázáno používat staré sety `0`, `1`, `2`, `3`, které jsou již několik let nahrazeny sety `default`, `knih`, `monografie`, `seri`. V příkazu `ListSets` je to již dlouhou dobu avizováno, v nejbližsí době ale budou tyto sety úplně odstraněny.

## 2021-11-26
https://support.kpsys.cz/issues/17369
Systém - Možnost používat více typů čárových kódu najednou. Pokud například knihovna akceptuje více typů čtenářských průkazek a každý typ má jiný typ čárového kódu. Pak je nutné vyjmenovat všechny typy v ini `CTEN.UserBarCodeType`, popřípadě v `EXEMP.ExemplarBarCodeType`.

## 2021-11-16
https://support.kpsys.cz/issues/17301
Systém - Nové nastavení používaného čárového kódu. Místo validní délky se nyní v Portaru používá konkrétní typ čárového kódu.
Nastavení typu čárového kódu čtenáře: `UserBarCodeType` v INI sekci `CTEN`.
Nastavení typu čárového kódu exempláře: `ExemplarBarCodeType` v INI sekci `EXEMP`.
Podporované typy kódů: EAN 8, EAN 13, CODE 39.

## 2021-11-09
https://support.kpsys.cz/issues/16982
SIP2 - Systém nově po úspěšném vracení zobrazuje email zákonného zástupce, eventuálně hlavy rodiny, pokud čtenář žádný email nemá.

## 2021-11-05
Větší balík výkonostních vylepšení, řeší některé problémy se zaplnením paměti.

## 2021-11-03
https://support.kpsys.cz/issues/16887
Systém - Kompletně přepracována logika potvrzovacích všech dialogů v systému (např. při půjčování apod.), čímž je zvýšena robustnost a odolnost vůči vypnutí portara.

## 2021-11-02
https://support.kpsys.cz/issues/15522
Systém - Přepracován modul `Oblíbené`. Oblíbené záznamy jsou nyní specialní typ kolekce
a nejsou načítany všechny najednou, což opravuje problémy s jejich načítáním. 

## 2021-11-01
https://support.kpsys.cz/issues/15946
Frontend - Striktní zakázání používání maker `#f` a `#sf` (bez `$record` parametru). Nově je třeba vždy používat `#sfSep($record...`. Portaro původní formát při startu automaticky konvertuje na nový formát.

## 2021-10-26
https://support.kpsys.cz/issues/17081
SAML2 IdP - Přidána podpora pro DNNT service providery NDK, MZK a KNAV

## 2021-10-08
https://support.kpsys.cz/issues/17082
Systém - Opraven OOM (Out Of Memory) při načítání záznamů, které jsou navázány na stovky částí nebo článků - omezením maximálního počtu načítaných částí/článků na 100

https://support.kpsys.cz/issues/17107
Obálky - načítání zástupných obálek je nyní tzv. department-aware, tedy, podporuje různé obálky pro různá oddělení

## 2021-10-06
Frontend - Odstraněna podpora webového prohlížeče Microsoft Internet Explorer 11 (IE 11)
a Microsoft Edge Legacy (Starší verze Microsoft Edge) - **jedná se o verze Edge vydané před rokem 2020**.

## 2021-10-01
https://support.kpsys.cz/issues/15325
Systém - Přidán changelog

## 2021-09-30
https://support.kpsys.cz/issues/16988
OAI - Přidána podpora pro `{allowedExemplarStatuses}` proměnnou v OAI set sql šabloně