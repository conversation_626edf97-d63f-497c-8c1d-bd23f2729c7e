<script lang="ts">
    import type {AjaxService} from 'core/data-services/ajax.service';
    import {getInjector} from 'core/svelte-context/context';
    import {AjaxServiceProvider} from 'core/data-services/ajax.service';
    import {onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import KpLoadablePageContainer from 'shared/layouts/containers/KpLoadablePageContainer.svelte';
    import KpMarkdown from 'shared/components/kp-markdown/KpMarkdown.svelte';

    const ajaxService = getInjector().getByToken<AjaxService>(AjaxServiceProvider.providerName);

    let changelogMd: string | undefined;

    onMount(async () => {
        changelogMd = await ajaxService.withoutBaseUrl().createRequest('/docs/changelog/changelog.md')
            .withResponseType('text')
            .get();

        console.log(changelogMd);
    });
</script>

<KpLoadablePageContainer pageClass="kp-changelog-page" loading="{!exists(changelogMd)}">
    <KpMarkdown markdown="{changelogMd}"/>
</KpLoadablePageContainer>

<style lang="less">
    @code-highlight: #f5e7e2;
    @code-padding: 3px 5px;

    :global {
        .kp-changelog-page > * {
            margin: 0;
        }

        .kp-changelog-page > h2 {
            margin-bottom: -14px;
        }

        .kp-changelog-page code {
            color: var(--brand-orange-new);
            background-color: @code-highlight;
            padding: @code-padding;
        }
    }
</style>