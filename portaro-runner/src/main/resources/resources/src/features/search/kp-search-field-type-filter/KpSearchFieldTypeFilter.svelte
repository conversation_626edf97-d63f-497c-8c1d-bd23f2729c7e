<script lang="ts">
    import type {FieldTypeId, RecordSearchParams, ViewableSearch} from 'typings/portaro.be.types';
    import type {FieldTypeDefinition, FondFieldTypeDefinitions} from 'src/features/record/field-types/types';
    import {getSearchFieldTypeFiltersContextIfExists} from 'src/features/search/kp-search-field-type-filter/search-field-type-filters.context';
    import {onDestroy} from 'svelte';
    import {getSearchContext} from 'src/features/search/kp-search-context/search-context';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import {ValueEditorSize} from 'shared/value-editors/types';
    import {get} from 'svelte/store';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpPopover from 'shared/ui-widgets/popover/KpPopover.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let fieldTypeId: FieldTypeId;

    const context = getSearchFieldTypeFiltersContextIfExists();
    const searchContext = getSearchContext<unknown, RecordSearchParams>();
    const supportedFilteringDatatypes = ['TEXT', 'NUMBER', 'DATE'];

    let lastSearch: ViewableSearch<RecordSearchParams, unknown>;
    const lastSearchSubscription = searchContext.getLastSearch$().subscribe((currentLastSearch) => lastSearch = currentLastSearch);

    let fieldDefinition: FieldTypeDefinition | null = null;
    const fieldTypeDefinitionsUnsubscribe = exists(context) ? context.fieldTypeDefinitions$.subscribe((currentFieldTypeDefinitions) => {
        if (exists(currentFieldTypeDefinitions)) {
            fieldDefinition = getFieldTypeDefinitionRecursively(currentFieldTypeDefinitions);
        }
    }) : undefined;

    let filterModel: string | null = null;

    onDestroy(() => {
        cleanup(fieldTypeDefinitionsUnsubscribe, lastSearchSubscription);
    });

    function getFieldTypeDefinitionRecursively(fieldTypeDefinitions: FondFieldTypeDefinitions): FieldTypeDefinition | null {
        if (fieldTypeDefinitions.fieldTypes.length === 0) {
            return null;
        }

        const fieldTypeDef = fieldTypeDefinitions.fieldTypes.find((fieldType) => fieldType.id === fieldTypeId);

        if (exists(fieldTypeDef)) {
            return fieldTypeDef;
        }

        for (const fieldType of fieldTypeDefinitions.fieldTypes) {
            const subfieldTypeDef = getFieldTypeDefinitionRecursively(fieldType);

            if (exists(subfieldTypeDef)) {
                return subfieldTypeDef;
            }
        }

        return null;
    }

    function createSorting(sorter: string, ascending: boolean) {
        searchContext.newSearchWithPartialParams({
            sorting: {
                id: `${ascending ? '' : '-'}${sorter}`,
                text: sorter,
                field: sorter,
                asc: ascending
            }
        });
    }

    function createFilter(filterParam: string, filterValue: string) {
        context.setFilteredValue(filterParam, filterValue);
    }

    function cancelSorting(sorter: string) {
        const params = searchContext.getCompleteParams();
        if (!exists(params.sorting) || params.sorting.field !== sorter) {
            return;
        }

        searchContext.newSearchWithPartialParams({
            sorting: null
        });
    }

    function cancelFilter(filterParam: string) {
        context.removeFilteredValue(filterParam);
        filterModel = null;
    }

    function isSortingActive(sorter: string): boolean {
        const params = searchContext.getFrontendParams();
        return exists(params.sorting) && params.sorting.field === sorter;
    }

    function isSortingAsc(sorter: string): boolean {
        const params = searchContext.getFrontendParams();
        return exists(params.sorting) && params.sorting.field === sorter && params.sorting.asc;
    }

    function isFilterActive(filterParam: string): boolean {
        const filteredValues = get(context.filteredValues$);
        return filterParam in filteredValues;
    }

    function isFilteringSupported(fieldDef: FieldTypeDefinition): boolean {
        return exists(fieldDef.editor?.defaultValue) && supportedFilteringDatatypes.some((datatypeName) => datatypeName === fieldDef.datatype?.name);
    }

    function getCurrentFilterValue(): string | number | null {
        const value = searchContext.getFrontendParams()[fieldDefinition.valueFilterSearchParam];
        filterModel = value;
        return value;
    }

    const handleFilterModelChange = (event: CustomEvent<string | number>) => {
        if (typeof event.detail === 'number') {
            filterModel = event.detail.toString(10);
            return;
        }

        filterModel = event.detail;
    };

    const handleFilterSubmit = () => {
        if (!filterModel || filterModel.trim().length === 0) {
            return;
        }

        createFilter(fieldDefinition.valueFilterSearchParam, filterModel);
    };

    $: filterModelValueSubmittable = filterModel && filterModel.trim().length > 0;
    $: sortingSupported = exists(fieldDefinition) && exists(fieldDefinition.valueSorter);
    $: filteringSupported = exists(fieldDefinition) && exists(fieldDefinition.valueFilterSearchParam) && isFilteringSupported(fieldDefinition);
    $: sortingActive = exists(lastSearch) && exists(fieldDefinition) && isSortingActive(fieldDefinition.valueSorter);
    $: sortingAsc = exists(lastSearch) && exists(fieldDefinition) && isSortingAsc(fieldDefinition.valueSorter);
    $: filterActive = exists(lastSearch) && exists(fieldDefinition) && isFilterActive(fieldDefinition.valueFilterSearchParam);
</script>

{#if exists(context) && exists(fieldDefinition) && (sortingSupported || filteringSupported)}
    <span class="kp-search-field-type-filters">
        <slot/>

        <Spacer direction="horizontal" size="xs"/>

        {#if sortingSupported}
            <KpPopover additionalPopoverButtonClasses="field-type-filter-button {sortingActive ? 'filter-active' : ''}" buttonSize="xs" buttonStyle="no-background">
                <svelte:fragment slot="button">
                    <UIcon icon="{sortingActive ? (sortingAsc ? 'arrow-small-up' : 'arrow-small-down') : 'sort'}"/>
                </svelte:fragment>

                <svelte:fragment slot="popover-content">
                    <Flex direction="column" gap="s">
                        <KpButton buttonSize="sm" on:click={() => createSorting(fieldDefinition.valueSorter, true)}>
                            <IconedContent icon="arrow-small-up">Vzestupně</IconedContent>
                        </KpButton>

                        <KpButton buttonSize="sm" on:click={() => createSorting(fieldDefinition.valueSorter, false)}>
                            <IconedContent icon="arrow-small-down">Sestupně</IconedContent>
                        </KpButton>

                        {#if sortingActive}
                            <KpButton buttonSize="sm" buttonStyle="danger-new" on:click={() => cancelSorting(fieldDefinition.valueSorter)}>
                                <IconedContent icon="cross-circle">Zrušit řazení</IconedContent>
                            </KpButton>
                        {/if}
                    </Flex>
                </svelte:fragment>
            </KpPopover>
        {/if}

        {#if filteringSupported}
            <KpPopover additionalPopoverButtonClasses="field-type-filter-button {filterActive ? 'filter-active' : ''}" buttonSize="xs" buttonStyle="no-background">
                <svelte:fragment slot="button">
                    <UIcon icon="search"/>
                </svelte:fragment>

                <svelte:fragment slot="popover-content">
                    <Flex direction="column" gap="s">
                        <form class="editor-container" on:submit|preventDefault="{handleFilterSubmit}">
                            <KpValueEditor {...fieldDefinition.editor.defaultValue}
                                           isDisabled="{false}"
                                           model="{getCurrentFilterValue()}"
                                           size="{ValueEditorSize.SM}"
                                           on:model-change={handleFilterModelChange}/>

                            <KpButton buttonSize="sm" isDisabled="{!filterModelValueSubmittable}" buttonType="submit" isBlock>
                                <IconedContent icon="filter">Filtrovat</IconedContent>
                            </KpButton>
                        </form>

                        {#if filterActive}
                            <KpButton buttonSize="sm" buttonStyle="danger-new" on:click={() => cancelFilter(fieldDefinition.valueFilterSearchParam)}>
                                <IconedContent icon="cross-circle">Zrušit filtr</IconedContent>
                            </KpButton>
                        {/if}
                    </Flex>
                </svelte:fragment>
            </KpPopover>
        {/if}
    </span>
{:else}
    <slot/>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .kp-search-field-type-filters {
        display: inline-flex;
        align-items: center;

        .editor-container {
            display: flex;
            flex-direction: column;
            gap: @spacing-s;
        }
    }

    :global {
        .kp-search-field-type-filters .field-type-filter-button {
            font-size: @font-size-small;
            color: @themed-text-muted-label;
            transition: color 0.3s ease-in-out;

            &.filter-active {
                color: var(--brand-orange-new);
            }
        }
    }
</style>