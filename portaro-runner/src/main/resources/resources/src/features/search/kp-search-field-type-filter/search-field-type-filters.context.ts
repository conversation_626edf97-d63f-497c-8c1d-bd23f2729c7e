import type {Readable} from 'svelte/store';
import type {FondFieldTypeDefinitions} from 'src/features/record/field-types/types';
import {writable} from 'svelte/store';
import {getContext, hasContext, setContext} from 'svelte';

const contextKey = 'search-field-type-filters-context';

export interface SearchFieldTypeFiltersContext {
    fieldTypeDefinitions$: Readable<FondFieldTypeDefinitions | null>;
    filteredValues$: Readable<Record<string, string>>;
    setFieldTypeDefinitions: (fieldTypeDefinitions: FondFieldTypeDefinitions | null) => void;
    setFilteredValue: (filterParam: string, value: string) => void;
    removeFilteredValue: (filterParam: string) => void;
}

export function createSearchFieldTypeFiltersContext(): SearchFieldTypeFiltersContext {
    const fieldTypeDefinitions = writable<FondFieldTypeDefinitions | null>(null);
    const filteredValues = writable<Record<string, string>>({});

    return setContext<SearchFieldTypeFiltersContext>(contextKey, {
        fieldTypeDefinitions$: fieldTypeDefinitions,
        filteredValues$: filteredValues,
        setFieldTypeDefinitions: (newFieldTypeDefinitions) => fieldTypeDefinitions.set(newFieldTypeDefinitions),
        setFilteredValue: (filterParam, value) => filteredValues.update((currentFilteredValues) => {
            currentFilteredValues[filterParam] = value;
            return currentFilteredValues;
        }),
        removeFilteredValue: (filterParam) => filteredValues.update((currentFilteredValues) => {
            delete currentFilteredValues[filterParam];
            return currentFilteredValues;
        })
    });
}

export function getSearchFieldTypeFiltersContextIfExists(): SearchFieldTypeFiltersContext | null {
    if (!hasContext(contextKey)) {
        return null;
    }

    return getContext<SearchFieldTypeFiltersContext>(contextKey);
}