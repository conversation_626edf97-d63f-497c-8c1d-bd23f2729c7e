<script lang="ts">
    import type {FondFieldTypeDefinitions} from 'src/features/record/field-types/types';
    import type {RecordSearchParams} from 'typings/portaro.be.types';
    import type {Conjunction} from 'src/features/search/search-criteria/criteria.types';
    import {onDestroy, onMount} from 'svelte';
    import {getInjector} from 'core/svelte-context/context';
    import {createSearchFieldTypeFiltersContext} from 'src/features/search/kp-search-field-type-filter/search-field-type-filters.context';
    import {cleanup, exists} from 'shared/utils/custom-utils';
    import {FieldTypeDataService} from 'src/features/record/field-types/field-type-data.service';
    import {getSearchContext} from 'src/features/search/kp-search-context/search-context';

    export let fondId: number;
    export let fieldTypeDefinitions: FondFieldTypeDefinitions | null = null;

    const fieldTypeDataService = getInjector().getByClass(FieldTypeDataService);
    const context = createSearchFieldTypeFiltersContext();
    const searchContext = getSearchContext<unknown, RecordSearchParams>();

    const filteredValuesSubscription = context.filteredValues$.subscribe((filteredValues) => {
        searchContext.newSearchWithPartialParams({
            facetRestriction: createFacetRestrictions(filteredValues)
        });
    });

    onMount(async () => {
        if (exists(fieldTypeDefinitions)) {
            context.setFieldTypeDefinitions(fieldTypeDefinitions);
            return;
        }

        const fondFieldTypeDefinitions = await fieldTypeDataService.loadFondFieldTypeDefinitions(fondId);
        context.setFieldTypeDefinitions(fondFieldTypeDefinitions);
    });

    onDestroy(() => {
        cleanup(filteredValuesSubscription);
    });

    function createFacetRestrictions(filteredValues: Record<string, string>): Conjunction {
        if (Object.keys(filteredValues).length === 0) {
            return null;
        }

        return {
            and: Object.entries(filteredValues).map(([filterParam, value]) => {
                return {
                    field: {
                        id: filterParam,
                        text: filterParam
                    },
                    startsWithWords: {
                        value
                    }
                };
            })
        };
    }
</script>

<slot/>