<script lang="ts">
    import type {AjaxService} from 'core/data-services/ajax.service';
    import {AjaxServiceProvider} from 'core/data-services/ajax.service';
    import {getInjector} from 'core/svelte-context/context';
    import {onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import KpLoadablePageContainer from 'shared/layouts/containers/KpLoadablePageContainer.svelte';
    import KpMarkdown from 'shared/components/kp-markdown/KpMarkdown.svelte';

    const ajaxService = getInjector().getByToken<AjaxService>(AjaxServiceProvider.providerName);

    let apidocsMd: string | undefined;

    onMount(async () => {
        apidocsMd = await ajaxService.withoutBaseUrl().createRequest('/docs/apidocs/apidocs.md')
            .withResponseType('text')
            .get();

        console.log(apidocsMd);
    });
</script>

<KpLoadablePageContainer pageClass="kp-apidocs-page" loading="{!exists(apidocsMd)}">
    <KpMarkdown markdown="{apidocsMd}"/>
</KpLoadablePageContainer>

<style lang="less">
    @code-highlight: #f5e7e2;
    @code-padding: 3px 5px;

    :global {
        .kp-apidocs-page > * {
            margin: 0;
        }

        .kp-apidocs-page > h2 {
            margin-bottom: -14px;
        }

        .kp-apidocs-page code {
            color: var(--brand-orange-new);
            background-color: @code-highlight;
            padding: @code-padding;
        }
    }
</style>