# API docs for Portaro
{docdate}
:toc: left
:sectnums:
:toclevels: 2
:sectnumlevels: 2
:doctype: book
:icons: font
:source-highlighter: highlightjs
:verbisboxerStationHeartbeatInterval: 30s
:verbisboxerStationDownTimeout: 2min
:verbisboxerServerResponseTimeout: 20s
:verbisboxerStationRequestRetryCount: 2
:verbisboxerPinLength: 6
:verbisboxerUrlPrefix: https://verbis-boxer.kpsys.cz/verbisbox/v2

## Authentication

Authentication to Portaro uses standard OAuth2 client or authorization code flows.

### Get token

#### for client (e.g. Verbis app)

```http
POST /oauth/token HTTP/1.1
Content-Type: application/json; charset=UTF-8
Authorization: Basic <base64encodedclientandpassword>
Host: https://demo.kpsys.cz

grant_type:client_credentials
````

##### Example cURL

```bash
curl --request POST --url http://localhost/oauth/token --header "Authorization: Basic <base64encodedclientandpassword>" --data grant_type=client_credentials
```

#### for user via client

```http
POST /oauth/token HTTP/1.1
Content-Type: application/json; charset=UTF-8
Authorization: Basic <base64encodedclientandpassword>
Host: https://demo.kpsys.cz

grant_type:password
username:<knihovnikovo_jmeno>
password:<knihovnikovo_heslo>
```

##### Example cURL

```bash
curl --request POST --url http://localhost/oauth/token --header "Authorization: Basic <base64encodedclientandpassword>" --data grant_type=password --data username=<knihovnikovo_jmeno> --data password=<knihovnikovo_heslo>
```

## Response

### Example

```json
{
  "access_token": "<access_token>",
  "token_type": "bearer",
  "expires_in": 43199,
  "scope": "EVIDED",
  "jti": "dd6d6306-a13e-46d6-8379-92b9096fbd13"
}
```

## Subsequent authorized calls

To all requests, add header

```
Authorization: Bearer <access_token>
```

### Example cURL

```bash
curl --request GET --url http://localhost/api/users/current --header "Authorization: Bearer <access_token>"
```

## Search

Common entrypoint for all search is `/search`. Main parameter for specifying what to search is `kind`.

Common parameters

```
- pageSize - page size used for pagination
- pageNumber - page number in pagination
- kind - what to search, values can be 'document', 'authority', 'exemplar', 'user'
- q - generic query parameter, used as global or 'whatewer'
- name - item name (document field 245, authority field 100, use person name, etc.)
```

### Document search

When search for documents, specify `kind=record&subkind=document` parameters. Specific parameters

#### Example 200

```http
GET /api/search?kind=record&subkind=document&rootFond=1&name=babicka HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz
Content-Length: 0
```

```json
{
    "title": "babicka",
    "subtitle": null,
    "type": "",
    "id": "LqvxiiCWQl-Je5RImqfiUw",
    "result": {
        "totalElements": 3,
        "duration": 281,
        "content": [
            {
                "fond": {
                    "id": 1,
                    "name": "Monografie",
                    "text": "Monografie"
                },
                "status": {
                    "id": 4,
                    "text": "Ukončená katalogizace"
                },
                "author": "aaaa bbbb",
                "isbn": null,
                "name": "Babička",
                "type": "document",
                "id": "63a6c870-cbdf-43cb-ad7b-12bd4af083b0",
                "kindedId": 97169,
                "text": "Babička",
                "exemplarable": true,
                "publisher": null,
                "lastUpdateDate": "2020-11-04T14:51:42+01:00",
                "cover": null,
                "directoryId": 282019,
                "volumeable": false,
                "periodical": false
            },
            {
                "fond": {
                    "id": 1,
                    "name": "Monografie",
                    "text": "Monografie"
                },
                "status": {
                    "id": 4,
                    "text": "Ukončená katalogizace"
                },
                "author": "Němcová, Božena,",
                "isbn": null,
                "name": "Babička",
                "type": "document",
                "id": "d7905d5d-0bc6-41b4-b3a8-b66c9af0a2f4",
                "kindedId": 4334,
                "text": "Babička",
                "exemplarable": true,
                "publisher": "Praha : Státní nakladatelství krásné literatury a umění, 1961",
                "lastUpdateDate": "2020-10-14T11:10:03+02:00",
                "cover": null,
                "directoryId": 19868,
                "volumeable": false,
                "periodical": false
            },
            {
                "fond": {
                    "id": 1,
                    "name": "Monografie",
                    "text": "Monografie"
                },
                "status": {
                    "id": 0,
                    "text": "Nedefinován"
                },
                "author": "Hejná, Olga,",
                "isbn": null,
                "name": "Tajemnica babci",
                "type": "document",
                "id": "f0e0a945-06f5-4fbb-aedb-c18bc6b0f164",
                "kindedId": 14,
                "text": "Tajemnica babci",
                "exemplarable": true,
                "publisher": "Warszawa : Nasza Księgarnia, 1972",
                "lastUpdateDate": "2020-10-19T09:25:32+02:00",
                "cover": {
                    "id": 42968,
                    "name": "obálka",
                    "text": "obálka"
                },
                "directoryId": 71034,
                "volumeable": false,
                "periodical": false
            }
        ],
        "numberOfElements": 3,
        "totalPages": 1,
        "pageNumber": 1,
        "pageSize": 10
    }
}
```

### Authority search

When search for authorities, specify `kind=record&subind=authority` parameter. Specific parameters

```
```

#### Example 200

```http
GET /api/search?kind=record&subkind=authority&rootFond=31&name=Božena+Němcová HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz
Content-Length: 0
```

```json
{
    "title": "Božena Němcová",
    "subtitle": null,
    "type": "",
    "id": "K9MwxcprTo2daXoKxvMHWw",
    "result": {
        "totalElements": 1,
        "duration": 138,
        "content": [
            {
                "name": "Božena Němcová",
                "type": "authority",
                "text": "Božena Němcová",
                "fond": {
                    "id": 31,
                    "name": "Jména osob",
                    "text": "Jména osob"
                },
                "id": "b6d4b384-08f8-4e7b-8497-13e76893985d",
                "kindedId": 191470,
                "status": {
                    "id": 13,
                    "text": "Vlastní autorita"
                },
                "lastUpdateDate": "2020-09-06T15:43:24+02:00",
                "cover": null,
                "directoryId": 282021
            }
        ],
        "numberOfElements": 1,
        "totalPages": 1,
        "pageNumber": 1,
        "pageSize": 30
    }
}
```

## Newest documents

Standalone endpoint for fetching newest documents (showing on portaro home page)

### Example

#### Example 200

```http
GET /api/newest-documents HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz
Content-Length: 0
```

```json
[
    {
        "id": "25a8d5a6-40aa-49ce-83a6-e7eccb185e9a",
        "name": "Chlorid sodný",
        "type": "document",
        "fond": {"id": 1, "name": "Monografie"},
        "author": "Adler-Olsen, Jussi,",
        "isbn": {
            "value": "9788027511587",
            "trimmedValue": "9788027511587"
        },
        "publisher": "Brno : Host, 2022",
        "publicationStartYear": {"value": 2022, "text": "2022"},
        "cover": {"id": 427344},
        "fields": [],
        "text": "Chlorid sodný"
    }
]
```

## Record resource

### Create or edit the record field

To create/edit a field or subfield, you must specify fieldId of that field

```url
PUT /api/v2/records/{recordId}/fields/{fieldId}
```

where

- `recordId` is record uuid, e.g. `517458e6-0a48-4af0-a33f-4089f87ccfda`
- `fieldId` e.g. `d245#0.a#0`
- request body is json with value to set in `value` field

#### Example 200

```http
POST /api/v2/records/4949eac9-dfee-4256-8f51-56f42c5464a0/fields/d245#0.a#0 HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz

{
  "value":"Headway"
}
```

Complex authority field (code `main`)

```http
POST /api/v2/records/4949eac9-dfee-4256-8f51-56f42c5464a0/fields/d100#0.main#0 HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz

{
  "value":{
    "id":"fb8f6957-f17b-4f09-86e1-bdfdfd7519b8"
  }
}
```

Simple authority field (code `a`) by unknown record - specify string subfield value

```http
POST /api/v2/records/4949eac9-dfee-4256-8f51-56f42c5464a0/fields/d655#0.a#0 HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz

{
  "value": {
    "fieldLabel": "Beletrie"
  }
}
```

Complex authority field (code `main`) by unknown record - specify values for particular subfield codes (e.g. 100.a, 100.b, 100.c)

```http
POST /api/v2/records/4949eac9-dfee-4256-8f51-56f42c5464a0/fields/d100#0.main#0 HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz

{
  "value": {
    "fieldLabels": {
      "d100.a": "Němcová, Božena",
      "d100.d": "1820-1862"
    }
  }
}
```

## Record editation resource

For a complex manipulation with record, use `/api/record-editations` endpoint. Firstly, create an record editation for easy fields manipulation. Then, you can add or remove new fields, add or edit field values. Each field gets own unique id. With this id you can address any of field.

### Create record editation

Creates new editation session with given record

```url
POST /api/record-editations
```

with request body

```json
{
  "subkind": {kind},
  "fond": {fond},
  "revisionSavingPolicy": {revisionSavingPolicy},
  "emptyFieldsCompletion": {emptyFieldsCompletion},
}
```

where

- `kind` is type of record: `document` or `authority`
- `fond` is id of desired record fond, e.g. `1` or `53`
- `revisionSavingPolicy` is policy, when to automatically save record, possible values are
    * `manual-only` - system will never save record automatically - after finish editation, you will have to call save or publish
    * `after-each-change-when-draft` - system will save record after any change, when record is DRAFT (not published), after publish, you will have to call save manually
    * `after-each-change-always` - system will save record after any change automatically (no need for manual calling save or publish)
- `emptyFieldsCompletion` is whether to automatic empty fields creation, `true` or `false`, default `true`

#### Example 200

```http
POST /api/record-editations HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz

{
  "subkind": "document",
  "fond": 1,
  "revisionSavingPolicy": "manual-only",
  "emptyFieldsCompletion": false
}
```

```json
{
  "id": "sP5NmmLPQVqMGG-EMxqecg",
  "text": "",
  "recordId": "1283f867-3ff8-446f-b3a1-e67ce20cd21c",
  "directoryId": 482527,
  "type": "document",
  "draft": false,
  "revisionSaved": true,
  "fond": {
    "id": 1,
    "text": "Monografie"
  },
  "fields": [
    {
      "id": "96ab9ae4-8764-4fca-9a3e-a78a76e85a30",
      "fieldRepetition": 0,
      "ind1": "0",
      "ind2": "0",
      "empty": true,
      "fieldId": "245#0",
      "text": "",
      "code": "245",
      "typeId": "d245",
      "fieldTypeText": "Údaje o názvu",
      "fields": [
        {
          "id": "4cc32a88-4246-409e-8931-850b61db1ceb",
          "value": null,
          "empty": true,
          "fieldId": "245#0.a#0",
          "text": "",
          "url": false,
          "recordLink": false,
          "raw": null,
          "recordId": null,
          "typeId": "d245.a",
          "code": "a",
          "fieldTypeText": "Název"
        },
        {
          "id": "6324477d-3491-41ec-a77d-01371227611d",
          "value": null,
          "empty": true,
          "fieldId": "245#0.b#0",
          "text": "",
          "url": false,
          "recordLink": false,
          "raw": null,
          "recordId": null,
          "typeId": "d245.b",
          "code": "b",
          "fieldTypeText": "Další údaje o názvu"
        },
        {
          "id": "e9ee2815-ab07-4b53-a8de-73e701e433b1",
          "value": null,
          "empty": true,
          "fieldId": "245#0.c#0",
          "text": "",
          "url": false,
          "recordLink": false,
          "raw": null,
          "recordId": null,
          "typeId": "d245.c",
          "code": "c",
          "fieldTypeText": "Údaj o odpovědnosti atd."
        }
      ]
    }
  ]
}
```

### Publish record editation

Publish and save finished record editation session

```http
POST /api/record-editations/sP5NmmLPQVqMGG-EMxqecg/publish HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz
```

### Set record catalogization phase

```url
POST /api/records/status

{
    record: string,
    status: number
}
```

where

- `record` is record uuid, e.g. `517458e6-0a48-4af0-a33f-4089f87ccfda`
- `status` is desired record status, valid value is one of:
    * `3` — represent status "standard document" state
    * `4` — represent "finished cataloging" state

<!-- end list -->

```http
POST /api/records/status HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz

{
  "record": "517458e6-0a48-4af0-a33f-4089f87ccfda",
  "status": 4
}
```

### Set record to be visible on given department

After record is created, you hava to specify, where it can be visible. So, create record "holding" on given department.

```http
POST /api/record-holdings HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz

{
    "record": "1283f867-3ff8-446f-b3a1-e67ce20cd21c",
    "department": 26
}
```

Response

```json
{
  "savedObject": {
    "id": "6713191e-f9cc-47a7-9307-7d7cffbb0b44"
  }
}
```

## Record bulk edit resource

### Bulk upsert record fields

For a bulk editation of one or more fields in one or more records

```url
/api/records/fields/bulkedit/complex
```

#### Example 200

If we want to upsert (create or replace) field `856` with value `https://neco.cz` in subfield `u` and value `plný text` in subfield `y`. If subfield `856.u` with value `https://neco.cz` is already present in record, it will be reused and updated with `plný text` to subfield `y`.

```http
POST /api/records/fields/bulkedit/complex
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz
```

```json
{
    "reusing": [{"id": "dkid:23543"}],
    "value": [
        {
            "reusing": [{
                "type": "d856",
                "havingField": {"type": "d856.u", "value": "https://neco.cz"}
            }],
            "set": {
                "type": "d856",
                "value": [
                    {
                        "reusing": [{"type": "d856.u"}],
                        "set": {"type": "d856.u", "value": "https://neco.cz"}
                    },
                    {
                        "reusing": [{"type": "d856.y"}],
                        "set": {"type": "d856.y", "value": "plný text"}
                    }
                ]
            }
        }
    ]
}
```

where

- `reusing` is object, which defines record(s) or field(s), which will be edited ("deduplicated"). If no record/field is found, new record/field will be created.
- `reusing.type` type id of field, which we want to edit
- `reusing.havingField` if we want to specify, that field we want to edit must have subfield of given type and value

## Sync resource

### Sync single user by external-system user identifier

To sync single user

```url
POST /api/sync/user
```

Response: `FinishedUserSyncResponse` (subclass of `ActionResponse`)

```
FinishedUserSyncResponse:
{
    responseType: "FinishedUserSyncResponse",
    text: string,
    items: ItemSyncResult[]
}

ItemSyncResult:
{
    item?: LabeledIdentified,
    way: string
}

LabeledIdentified:
{
    id: string | number,
    text: string
}

SyncWay:
"CREATE" | "CREATE_DRAFT" | "UPDATE" | "DELETE" | "SKIP"
```

#### Example 200

```http
POST /api/sync/user HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz

{
  "identifier": "ZAM1XYZ"
}
```

```json
{
    "text": "User synced with identifier ZAM1XYZ",
    "responseType": "FinishedUserSyncResponse",
    "finished": true,
    "items": [{
        "item": {
            "id": 123456,
            "text": "Jessica Alba"
        },
        "way": "CREATE"
    }]
}
```

### Fetch-only single user by external-system user identifier

To fetch single user

```url
POST /api/sync/user/fetch
```

Response: `FinishedUserFetchResponse` (subclass of `ActionResponse`)

```
FinishedUserFetchResponse:
{
    responseType: "FinishedUserFetchResponse",
    text: string,
    items: ItemFetchResult[]
}

ItemFetchResult:
{
    item: UserEditationRequest
}
```

#### Example 200

```http
POST /api/sync/user/fetch HTTP/1.1
Content-Type: application/json; charset=UTF-8
Host: https://demo.kpsys.cz

{
  "identifier": "ZAM1XYZ"
}
```

```json
{
  "text": "User sync with identifier SCH0387",
  "responseType": "FinishedUserFetchResponse",
  "finished": true,
  "items": [
    {
      "item": {
        "id": 31230,
        "active": false,
        "readerAccounts": [
          {
            "readerCategory": {
              "id": "IST",
              "text": "Interní-student"
            },
            "cardNumber": "SCH0387",
            "registrationDate": "2021-09-08T00:00:00+02:00",
            "registrationExpirationDate": "9999-12-31T00:00:00+01:00"
          }
        ],
        "editorAccounts": null,
        "emails": [
          {
            "value": "<EMAIL>",
            "source": {
              "id": "external-unis",
              "text": "Unis"
            }
          }
        ],
        "phoneNumbers": [
          {
            "value": "+************",
            "smsCapable": true,
            "source": {
              "id": "external-unis",
              "text": "Unis"
            }
          }
        ],
        "addresses": [
          {
            "permanent": true,
            "mailing": true,
            "street": "Peškova 523/12",
            "city": "Olomouc",
            "state": "Česká republika",
            "postalCode": "77900"
          }
        ],
        "username": null,
        "readableDepartments": null,
        "firstName": "Rostislav",
        "lastName": "Schwarz",
        "prefixDegree": null,
        "suffixDegree": null,
        "birthDate": "1988-03-09T00:00:00+01:00",
        "guId": "SCH0387",
        "netId": "SCH0387",
        "openidId": null,
        "bakalariId": "040449B2BC6380",
        "edookitId": null,
        "edupageId": null,
        "jobName": null,
        "jobAddress": null,
        "educationLevel": "FEI / Fakulta elektrotechniky a informatiky",
        "schoolClass": "K / B",
        "identityCardNumber": null,
        "kind": "PERSON"
      }
    }
  ]
}
```

## VerbisBox

- **Station** je celá stanice/sestava skládající se z x boxů, může se skládat z několika sloupců. Každý station má z výroby vygenerované UUIDv4, dále jako `stationId`.
- **Box** je jedna schránka. Každý box má také z výroby vygenerované UUIDv4, dále jako `boxId`.

`stationId`, `boxId` a basic auth password budou poskytnuta k nastavení do knihovního systému.

### Communication

Systém komunikace stanice s knihovním systémem je stylem klient-server (klient je stanice) pomocí protokolu HTTPS.
Stanice posílá serveru události a server vrací seznam příkazů, které stanice provádí. Například, po zadání pinu na stanici o tom stanice posílá request a server vrací příkaz na otevření boxu.

Adresy requestů mají definovatelnou doménu a prefix cesty, ta se může měnit. Aktuálně ale je `{verbisboxerUrlPrefix}`.

Komunikace probíhá ve formátu JSON.

- Jestliže server/klient dostane v odpovědi neznámé/nezdokumentované fieldy, ignoruje je.
- Všechny hodnoty jsou case-sensitive.
- Všechna UUIDv4 jsou lowercase.
- Pokud není explicitně řečeno jinak, všechny hodnoty jsou not-null.

Každý request obsahuje field `event`, ve kterém je stanicí vygenerovaníé UUIDv4 události, dále jako `eventId`.

V případě, že server do {verbisboxerServerResponseTimeout} neodpoví, stanice zkusí stejnou (i se stejným `eventId`) zprávu poslat ještě {verbisboxerStationRequestRetryCount}x.
Pokud ani na posledni request server neodpoví, stanice se nastaví do deaktivovaného režimu. Dále posílá pouze heartbeat (viz níže).

#### Autentikace

Klient (station) se autentikuje v každém requestu pomocí basic auth, tedy

```
Authorization: Basic a2FyZWw6aGVzbG8=
```

, kde username je `stationId` stanice, tedy např. `9a64be42-2f1f-4451-915f-dc6cc69aa789`, password je náhodně vygenerovaný, 20-ti místný alfanumerický řetězec.

##### Testovací uživatel

- Username: `9a64be42-2f1f-4451-915f-dc6cc69aa789`
- Password: `kVyqwcqfP4uPCbhn4RER`

#### HTTP hlavičky

Klient (station), kromě `Authorization`, posílá i hlavičky

```
Content-Type: application/json
Accept: application/json
User-Agent: VerbisBox/{version}
```

, kde místo `\{version\}` bude aktuální verze SW stanice, ideálně (ne nutně) v SemVer formátu, např. `4.1.35`.

##### Accept-Language

Klient (station) MŮŽE posílat i hlavičku `Accept-Language` podle specifikace, viz např. https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Language

##### X-Verbis-Trace-Id

Klient (station) MŮŽE posílat i hlavičku `X-Verbis-Trace-Id` sloužící jako MDC (Mapped Diagnostic Context) pro identifikaci akce/requestu v logu. Stanice by měla pro každý request vygenerovat UUID a v této hlavičce ho posílat. Všechny řádky v logu portara v rámci tohoto requestu budou toto UUID obsahovat. Tím lze jednoduše vyfiltrovat jen logy relevantní pro daný request

Log je z webu dostupný pod knihovnickým účtem na stránce `/actuator/logfile`.

### State

Stanice posílá pravidelně echo o svem stavu. Maximální interval bude daný nastavením stanice, defaultně {verbisboxerStationHeartbeatInterval}. Maximální znamená, že stanice může echo poslat dříve, např. okamžitě při nastalé chybě.

Stav obsahuje jak aktualní "health" stanice, tak stavy jednotlivých boxů. Boxy stanice posílá všechny, což se může v budoucnu změnit kvůli optimalizaci.

```
POST /state
```

```json
{
  "station": "9a64be42-2f1f-4451-915f-dc6cc69aa789",
  "st": "UP",
  "errorMessage": null,
  "inputVoltage": 11.7,
  "internetSignalStrength": 67,
  "boxes": [
    {
      "id": "bad0cc90-54d2-4267-8e2a-9737a3e5b1f8",
      "st": "OPEN"
    },
    {
      "id": "b8ec8f91-2835-4f16-a358-6426e0bc0308",
      "st": "CLOSED"
    }
  ],
  "event": "b171a575-ba4c-48b4-a6bb-3d0670c8ec13"
}
```

- Field `st` nabývá hodnot `UP` a `DOWN`.
- Field `errorMessage` je nullable.
- Field `inputVoltage` je not null JSON number. Udává vstupní napětí (z baterie nebo z adaptéru) ve voltech.
- Field `internetSignalStrength` je not null JSON integer (celočíselný). Udává procentuální sílu signálu použitého pro připojení k internetu. Pokud je stanice připojnena drátem, hodnota bude 100.
- Field `boxes[].st` nabývá hodnot `OPEN` a `CLOSED`.

Pokud je stanice v chybovém stavu, posílá (pokud je toho schopna) stav také:

```
POST /state
```

```json
{
  "station": "9a64be42-2f1f-4451-915f-dc6cc69aa789",
  "st": "DOWN",
  "errorMessage": "Low voltage",
  "boxes": [
    {
      "id": "bad0cc90-54d2-4267-8e2a-9737a3e5b1f8",
      "st": "OPEN"
    },
    {
      "id": "b8ec8f91-2835-4f16-a358-6426e0bc0308",
      "st": "CLOSED"
    }
  ],
  "event": "ba0cb1ce-2b6a-4162-83d2-70e76f4fe929"
}
```

Server může odpovědí vynutit např. nastavení textu displeje

Response 200

```json
{
  "type": "wait-for-input",
  "displayMessages": ["Zadejte PIN"]
}
```

Nebo v případě chyby, dočasně deaktivovat stanici

Response 200

```json
{
  "type": "station-disable",
  "displayMessages": ["Stanice je vypnuta"]
}
```

Jestliže server nedostane od stanice po {verbisboxerStationDownTimeout} žádný heartbeat request, stanice se považuje za vypnutou/nedostupnou. Verbis zakáže jakékoliv akce s boxem a upozorní administrátory.

Jestliže naopak stanice nedostane od serveru po {verbisboxerServerResponseTimeout} odpověď, server se považuje za vypnutý/nedostupný. Stanice nastaví na displeji text "Stanice je vypnuta" a zakáže jakékoliv operace.

### Vložení objednávky do boxu

Knihovník příjde ke stanici, naskenuje knihu. Stanice pošle serveru request s typem identifikátoru: `BAR_CODE` nebo `RFID` a hodnotou.

```
POST /item-identify
```

```json
{
  "station": "9a64be42-2f1f-4451-915f-dc6cc69aa789",
  "identifierValue": "1234567890",
  "identifierType": "BAR_CODE",
  "event": "b3118eb2-41cf-44c6-8f3b-e2dc85913df4"
}
```

Response 200

```json
{
  "type": "box-open",
  "boxes": ["bad0cc90-54d2-4267-8e2a-9737a3e5b1f8"],
  "displayMessages": ["Otevírám schránku..."]
}
```

Na základě `box-open` se otevře box s id `bad0cc90-54d2-4267-8e2a-9737a3e5b1f8` a na displeji se zobrazí informační text "Vložte knihu do schránky a zavřete".
Po otevření boxu (nebo všech boxů) stanice posílá `/state` s aktuálním stavem otevřenosti boxů.

```
POST /state
```

Response 200

```json
{
  "type": "wait-for-closing",
  "boxes": ["bad0cc90-54d2-4267-8e2a-9737a3e5b1f8"],
  "displayMessages": [
    "Vložte knihu",
    "do schránky",
    "a zavřete"
  ],
  "cancellable": true
}
```

Knihovník vloží knihu do schránky a schránku zavře. Zavřením stanice pošle serveru opět request `/state` s aktuálním stavem otevřenosti boxů.

```
POST /state
```

Response 200

```json
{
  "type": "wait-for-input",
  "displayMessages": ["Zadejte PIN"]
}
```

Do jednoho boxu může knihovník vložit více knih, každou knihu ale vkládá samostatně. Po každé musí box zavrít a otevřít, aby se eliminovaly lidské chyby.

V této chvíli má knihovník možnost akci zrušit stisknutím tlačítka "zrušit", ještě před uzavřením boxu.
Stisknutím tlačítka "zrušit" stanice pošle serveru request

```
POST /cancel
```

```json
{
  "station": "9a64be42-2f1f-4451-915f-dc6cc69aa789",
  "event": "b35d890f-a8f4-4ce9-a2c8-3cc9e213df2f"
}
```

Server v takovém případě ví, který box je otevřený a tedy ví, která akce (v tomto případě objednávka) se ruší.

Response 200

```json
{
  "type": "wait-for-closing",
  "boxes": ["bad0cc90-54d2-4267-8e2a-9737a3e5b1f8"],
  "displayMessages": ["Zavřete schránku"]
}
```

### Vyzvednutí objednávky z boxu

Čtenář příjde ke stanici, zadá {verbisboxerPinLength}-místný PIN, který dostal např. SMSkou. Stanice pošle serveru request

```
POST /pin-enter
```

```json
{
  "station": "9a64be42-2f1f-4451-915f-dc6cc69aa789",
  "pin": "123456",
  "event": "150e9676-91cf-4870-a02a-2bdb9cf03884"
}
```

Response 200

```json
{
  "type": "box-open",
  "boxes": ["bad0cc90-54d2-4267-8e2a-9737a3e5b1f8"],
  "displayMessages": ["Otevírám schránku..."]
}
```

Na základě `box-open` se otevře box s id `bad0cc90-54d2-4267-8e2a-9737a3e5b1f8`, stanice posílá `/state` request a na displeji se zobrazí informační text "Odeberte knihy a zavřete".

```
POST /state
```

Response 200

```json
{
  "type": "wait-for-closing",
  "boxes": ["bad0cc90-54d2-4267-8e2a-9737a3e5b1f8"],
  "displayMessages": [
    "Odeberte knihy",
    "a zavřete"
  ]
}
```

Čtenář odebere knihy a schránku zavře. Zavřením stanice pošle serveru `/state` request

```
POST /state
```

Response 200

```json
{
  "type": "wait-for-input",
  "displayMessages": ["Zadejte PIN"]
}
```

### Chybové odpovědi

Jakýkoliv request může skončit s chybou. Chybové odpovědi podporují standardní HTTP status kódy (série 4XX a 5XX) a jsou ve standardizovaném formátu.
Field `type` je v případě chyby `exception`, `exceptionType` pak definuje konkretni chybu. Hodnoty v níže uvedených přikladech jsou jen orientační.

Např. v případě stisknutí tlačítka "zrušit" ve chvíli, kdy akci zrušit nelze:

Response 400

```json
{
  "type": "exception",
  "exceptionType": "CannotCancelException",
  "displayMessages": [
    "Tuto akci",
    "nelze zrušit"
  ]
}
```

Např. v případě chyby na serveru:

Response 500

```json
{
  "type": "exception",
  "exceptionType": "IllegalStateException",
  "displayMessages": [
    "Nastala chyba",
    "na serveru"
  ]
}
```
