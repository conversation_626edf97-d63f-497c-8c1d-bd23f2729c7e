import visitedPanelComponentModule from './kp-visited-panel.module';
import {createTestContext, render} from '../../../test-utils/utils';
import KpVisitedPanel from './KpVisitedPanel.svelte';
import {tick} from 'svelte';
import {mockData} from './test-data';
import recordModule from '../../../features/record/record.module';
import type {KpVisitedPanelPresenter} from './kp-visited-panel.presenter';
import IInjectorService = angular.auto.IInjectorService;
import IProvideService = angular.auto.IProvideService;
import type {FileService} from '../../file/file.service';
import {AngularManualInjector} from 'core/injector';
import coreModule from 'core/core.module';
import type CurrentAuthService from 'shared/services/current-auth.service';
import modalDialogsModule from 'shared/modal-dialogs/modal-dialogs.module';

describe('kp-visited-panel component tests', () => {

    let testContext: Map<string, any>;
    let presenterMock: Pick<KpVisitedPanelPresenter, 'getVisitedDocuments'>;
    let fileServiceMock: Pick<FileService, 'getFilePath'>;
    let currentAuthServiceMock: Pick<CurrentAuthService, 'isLoggedIn'>;
    let finishedResponseInteractionServiceMock;
    let modalConversationInterceptorMock;
    let walkerServiceMock;
    let localizationServiceMock;

    beforeEach(() => {
        presenterMock = {
            getVisitedDocuments: jasmine.createSpy().and.resolveTo([mockData])
        };

        fileServiceMock = {
            getFilePath: jasmine.createSpy().and.callFake((file: { id }) => `/files/${file.id}`)
        };

        currentAuthServiceMock = {
            isLoggedIn: jasmine.createSpy().and.callFake(() => false)
        };

        walkerServiceMock = {
            newPageOnContext: jasmine.createSpy().and.resolveTo('')
        }

        localizationServiceMock = {
            get: jasmine.createSpy().and.callFake((code: string) => code)
        }

        finishedResponseInteractionServiceMock = {
            test: jasmine.createSpy().and.callFake(() => null)
        }

        modalConversationInterceptorMock = {
            registerFieldEnablingConfirmationDialogFromExceptionHandler: jasmine.createSpy().and.callFake((exception) => exception)
        }

        angular.mock.module(recordModule, visitedPanelComponentModule, coreModule, modalDialogsModule, /*@ngInject*/ ($provide: IProvideService) => {
            $provide.value('kpVisitedDocumentsPresenter', presenterMock);
            $provide.value('currentAuthService', currentAuthServiceMock);
            $provide.value('fileService', fileServiceMock);
            $provide.value('walker', walkerServiceMock);
            $provide.value('localizationService', localizationServiceMock);
            $provide.value('finishedResponseInteractionService', finishedResponseInteractionServiceMock);
            $provide.value('modalConversationInterceptor', modalConversationInterceptorMock);
        });

        inject(/*@ngInject*/ ($injector: IInjectorService) => {
            testContext = createTestContext(new AngularManualInjector($injector));
        });
    });

    describe('empty visited document list', () => {

        beforeEach(() => {
            presenterMock.getVisitedDocuments = jasmine.createSpy().and.resolveTo([]);
        });

        it('should NOT render component', async () => {

            const {container, componentInstance, unmount} = render(KpVisitedPanel, {context: testContext});

            expect(componentInstance).toBeDefined();

            await tick();

            expect(container.children.length).toBe(0);

            unmount();
        });
    });

    describe('non-empty visited document list', () => {

        it('should render component content', async () => {

            const {container, componentInstance, unmount} = render(KpVisitedPanel, {context: testContext});
            expect(componentInstance).toBeDefined();

            await tick();

            expect(presenterMock.getVisitedDocuments).toHaveBeenCalledWith(true);

            const documents = container.querySelectorAll('div.document');
            expect(documents.length).toBeGreaterThan(0);

            unmount();
        });

        it('should render component content without last visited document', async () => {

            const {container, componentInstance, unmount} = render(KpVisitedPanel, {
                props: {showLastVisited: false},
                context: testContext
            });
            expect(componentInstance).toBeDefined();

            await tick();

            expect(presenterMock.getVisitedDocuments).toHaveBeenCalledWith(false);

            const documents = container.querySelectorAll('div.document');
            expect(documents.length).toBeGreaterThan(0);

            unmount();
        });
    });
});
