import type {AuthableUser, Identified, Message, SearchParams, User, UUID} from 'typings/portaro.be.types';

export type ThreadUser = User | AuthableUser | UUID;
export type ThreadPlacement = 'fullscreen-page' | 'popover';

export interface ThreadMessagesSearchParams extends SearchParams {
    thread: Thread;
}

export interface MessageReceivedEventData {
    threadId: UUID;
    threadName: string;
    message: Message;
}

// Entities
export interface Thread extends Identified<UUID> {
    name: string;
    participants: ThreadParticipant[];
    iconUrl?: string; // zatím žádnou nemá
}

export interface RichThread extends Thread {
    unreadMessagesCount: number;
    unreadMentionsCount: number;
}

export interface ThreadParticipant extends Identified<UUID> {
    user: User;
    administrator: boolean;
}

// Requests
export interface NewThreadMessageRequest {
    thread: Thread;
    content: string;
    withFiles?: boolean;
}

export interface PublishMessageRequest {
    message: Message;
}

export interface NewThreadRequest {
    name?: string;
    linkedRecord?: UUID;
    participants: ThreadUser[];
}

export interface UserJoinThreadRequest {
    thread: Thread;
}

export interface LeaveThreadRequest {
    thread: Thread;
}

export interface AddThreadParticipantsRequest {
    thread: Thread;
    participants: ThreadUser[];
}

export interface RemoveThreadParticipantsRequest {
    thread: Thread;
    participants: ThreadUser[];
}

export interface UpdateThreadRequest {
    thread: Thread;
    name: string;
}