<script lang="ts">
    import type {Auth, Department} from 'typings/portaro.be.types';
    import {getInjector} from 'core/svelte-context/context';
    import {ErpTopbarService} from 'src/features/erp/components/erp-topbar/erp-topbar.service';
    import {onDestroy} from 'svelte';
    import {cleanup} from 'shared/utils/custom-utils';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {filterHierarchicalData} from 'shared/utils/hierarchical-filter-utils';
    import KpPopover from 'shared/ui-widgets/popover/KpPopover.svelte';
    import KpSearchBar from 'shared/ui-widgets/search/KpSearchBar.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import DepartmentHierarchy from 'src/features/erp/components/erp-topbar/DepartmentHierarchy.svelte';

    const service = getInjector().getByClass(ErpTopbarService);

    let currentAuth: Auth;
    const currentAuthSubscription = service.getCurrentAuth$().subscribe((auth) => currentAuth = auth);

    let searchQuery = '';

    onDestroy(() => {
        cleanup(currentAuthSubscription);
    });

    const handleDepartmentSelect = async (event: CustomEvent<Department>) => {
        await service.changeCurrentDepartment(event.detail);
    };

    const handleSearch = (event: CustomEvent<string>) => {
        searchQuery = event.detail;
    };

    const handleSearchReset = () => {
        searchQuery = '';
    };

    $: filteredDepartments = filterHierarchicalData(currentAuth.readableHierarchicalDepartments, searchQuery);
</script>

{#if currentAuth.evided}
    <KpPopover>
        <svelte:fragment slot="whole-button" let:buttonId let:panelId let:floatingRef let:popoverOpen>
            <button class="current-department" id="{buttonId}" popovertarget="{panelId}" use:floatingRef>
                {pipe(service.currentDepartment, loc())}
                <span class="text-muted">({service.currentDepartment.id})</span>

                <div class="icon-container" class:opened={popoverOpen}>
                    <UIcon icon="angle-small-down"/>
                </div>
            </button>
        </svelte:fragment>

        <Flex class="department-selector-content" direction="column" slot="popover-content">
            <KpSearchBar placeholder="Hledat..."
                         height="32px"
                         forceResetButton="{searchQuery.length > 0}"
                         on:search={handleSearch}
                         on:reset={handleSearchReset}/>

            <Spacer direction="vertical" size="m"/>

            {#if filteredDepartments.length === 0}
                <span class="text-muted">Žádná divize nalezena</span>
            {/if}

            {#each filteredDepartments as department(department.id)}
                <DepartmentHierarchy {department} on:department-select={handleDepartmentSelect}/>
            {/each}

            <Spacer direction="vertical" size="s"/>
        </Flex>
    </KpPopover>
{:else}
    <span class="current-department">{pipe(service.currentDepartment, loc())}</span>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .current-department {
        display: flex;
        align-items: center;
        gap: @spacing-s;
        font-size: @font-size-sm;
        font-weight: 500;
        background: none;
        padding: 0;
        margin: 0;
        border: none;
        cursor: pointer;
        outline: none;
        color: var(--text-color);
        text-decoration: none;
    }

    .icon-container {
        transition: rotate 0.2s ease-in-out;
        font-size: @font-size-small;

        &.opened {
            rotate: -180deg;
        }
    }
</style>