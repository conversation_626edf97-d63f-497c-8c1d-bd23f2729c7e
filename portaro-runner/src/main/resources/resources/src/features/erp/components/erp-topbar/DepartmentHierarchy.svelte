<script lang="ts">
    import type {ViewableDepartmentsHierarchy} from 'src/features/departments/types';
    import type {Department} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {exists} from 'shared/utils/custom-utils';
    import {createEventDispatcher} from 'svelte';

    export let department: ViewableDepartmentsHierarchy;

    const dispatch = createEventDispatcher<{'department-select': Department}>();
</script>

<div class="department-hierarchy">
    <button on:click={() => dispatch('department-select', department)}>
        {pipe(department, loc())}
        <span class="text-muted">({department.id})</span>
    </button>

    {#if exists(department.children) && department.children.length > 0}
        <div class="subdepartments">
            {#each department.children as subdepartment}
                <svelte:self department="{subdepartment}" on:department-select/>
            {/each}
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .department-hierarchy {
        display: flex;
        flex-direction: column;
        gap: @spacing-s;

        button {
            background: none;
            padding: @spacing-xs;
            margin: 0;
            border: none;
            cursor: pointer;
            outline: none;
            color: var(--text-color);
            text-decoration: none;
            transition: color 0.2s ease-in-out;
            text-align: start;
            position: relative;

            &:before {
                content: '';
                position: absolute;
                left: -3px;
                right: -3px;
                top: 0;
                bottom: 0;
                border-radius: @border-radius-default;
                background-color: transparent;
                z-index: -1;
                transition: background-color 0.2s ease-in-out;
            }

            &:hover {
                color: var(--accent-blue-new);

                &:before {
                    background-color: @themed-body-bg-blue-highlighted;
                }
            }
        }

        .subdepartments {
            display: flex;
            flex-direction: column;
            gap: @spacing-s;
            margin-left: @spacing-m;
            padding-left: @spacing-m;
            border-left: 1px solid var(--accent-blue-new);
        }
    }
</style>