package cz.kpsys.portaro.config;

import cz.kpsys.portaro.appserver.GenericTableWriteSaver;
import cz.kpsys.portaro.appserver.dml.DmlAppserverService;
import cz.kpsys.portaro.commons.cache.CacheCleaningDeleter;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.convert.StringToIntegerConverter;
import cz.kpsys.portaro.commons.object.IdSettable;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.location.*;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.support.TransactionTemplate;


import java.util.List;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PlacementConfig {

    @NonNull
    NamedParameterJdbcOperations jdbcTemplate;
    @NonNull
    QueryFactory queryFactory;
    @NonNull
    DmlAppserverService dmlAppserverService;
    @NonNull
    ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull
    DepartmentAccessor departmentAccessor;
    @NonNull
    CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull
    SaverBuilderFactory saverBuilderFactory;
    @NonNull
    ConverterRegisterer converterRegisterer;
    @NonNull
    TransactionTemplate transactionTemplate;
    @NonNull
    CacheService cacheService;
    @NonNull
    NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;

    @Bean
    public Codebook<Location, Integer> locationLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedByJpa(LocationEntity.class, LocationEntity.Fields.order)
                .convertedEachBy(new EntityToLocationConverter())
                .staticCached(Location.class.getSimpleName())
                .build();
    }

    @Bean
    public ContextualProvider<Department, List<Location>> readableLocationsContextualProvider() {
        return new ReadableLocationsContextualProvider(
                departmentAccessor,
                departmentLocationRelationLoader()
        );
    }

    @Bean
    public DepartmentLocationRelationLoader departmentLocationRelationLoader() {
        return new SpringDbDepartmentLocationRelationLoader(
                jdbcTemplate,
                queryFactory,
                new AllByIdsLoadableByIdLoaderAdapter<>(departmentLoader),
                new AllByIdsLoadableByIdLoaderAdapter<>(locationLoader())
        );
    }

    @Bean
    public Saver<Location, Location> locationSaver() {
        return saverBuilderFactory.<Location, Integer>saver()
                .intermediateConverting(new LocationToEntityConverter())
                .idSetting(LocationEntity.class, new StringToIntegerConverter(), IdSettable::setId)
                .withClearedCacheName(Location.class.getSimpleName())
                .build();
    }

    @Bean
    public DepartmentLocationRelationDeleter departmentLocationRelationDeleter() {
        return new DepartmentLocationRelationDeleter(dmlAppserverService);
    }

    @Bean
    public Saver<LocationWithDepartments, LocationWithDepartments> locationWithDepartmentsSaver() {
        return new LocationWithDepartmentsDelegatingSaver(
                departmentLocationRelationDeleter(),
                locationSaver(),
                departmentLocationRelationSaver());
    }

    @Bean
    public Saver<List<DepartmentLocationRelation>, List<DepartmentLocationRelation>> departmentLocationRelationSaver() {
        return GenericTableWriteSaver.of(
                new DepartmentLocationRelationSaveTableWriteGenerator(),
                dmlAppserverService);
    }

    @Bean
    public Deleter<Location> locationDeleter(@NonNull Runnable saveTransactionAuthenticator) {
        var saver = new GenericHookableDeleter<>(
                new CacheCleaningDeleter<>(
                        new TransactionalDeleter<>(
                                new LocationDeleter(notAutoCommittingJdbcTemplate, queryFactory),
                                transactionTemplate
                        ),
                        cacheService.createCleanerFor(Location.class.getSimpleName())
                )
        );
        saver.addPreHook(saveTransactionAuthenticator);
        return saver;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        converterRegisterer
                .registerForIntegerId(Location.class, locationLoader());
    }
}
