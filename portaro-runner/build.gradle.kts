import com.github.gradle.node.npm.task.NpmSetupTask
import com.github.gradle.node.npm.task.NpmTask
import com.github.gradle.node.task.NodeSetupTask
import com.google.cloud.tools.jib.gradle.JibTask
import org.apache.commons.net.ftp.FTP
import org.apache.commons.net.ftp.FTPClient
import org.gradle.api.internal.TaskInputsInternal
import java.io.FileInputStream
import java.nio.file.Paths
import kotlin.io.path.isRegularFile
import kotlin.io.path.readText

fun uploadToFtp(file: File, pathsForFile: Set<String>, ftpHost: String, ftpRootDirectory: String, ftpUsername: String, ftpPassword: String) {
    val ftp = FTPClient()
    ftp.connect(ftpHost)
    ftp.enterLocalPassiveMode()
    ftp.login(ftpUsername, ftpPassword)
    ftp.setFileType(FTP.BINARY_FILE_TYPE)

    pathsForFile.forEach {
        val targetFilePath = it
        print("Uploading file ${file} to ftp://${ftpHost}${ftpRootDirectory}${targetFilePath} as user ${ftpUsername}")

        val reading = FileInputStream(file)
        ftp.storeFile(ftpRootDirectory + targetFilePath, reading)
        reading.close()

        println(" ...completed")
    }

    ftp.disconnect()
}

fun defineCacheInputsForFrontEnd(inputs: TaskInputsInternal) {
    //task can be effective cached after correct specification of input dirs
    // stylesheets
    inputs.dir(file("${frontendDirectory}/styles"))
    // fonts
    inputs.dir(file("${frontendDirectory}/fonts"))
    // external dependencies
    inputs.dir(file("${frontendDirectory}/ext"))
    // images
    inputs.dir(file("${frontendDirectory}/img"))
    // plugins
    inputs.dir(file("${frontendDirectory}/scripts"))
    // sources
    inputs.dir(file("${frontendDirectory}/src"))
    // build configuration
    inputs.dir(file("${frontendDirectory}/webpack"))
    // packages
    inputs.files(file("${frontendDirectory}/package.json"), file("${frontendDirectory}/package-lock.json"))
}

buildscript {
    repositories {
        mavenCentral()
        maven {
            setUrl("https://plugins.gradle.org/m2/")
        }
    }

    dependencies {
        classpath("commons-net:commons-net:3.+")
    }
}

plugins {
    id("com.github.node-gradle.node").version("7.1.0")
    id("com.gorylenko.gradle-git-properties").version("2.5.2")
    id("org.springframework.boot").version("3.5.4")
    id("com.google.cloud.tools.jib").version("3.4.5")
}

dependencies {
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.mockito:mockito-core:+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")
    implementation("org.mapstruct:mapstruct:+")
    annotationProcessor("org.mapstruct:mapstruct-processor:+")

    implementation(project(":portaro-acme"))
    implementation(project(":portaro-acquisition"))
    implementation(project(":portaro-appserver"))
    implementation(project(":portaro-auth"))
    implementation(project(":portaro-auth-anonymous"))
    implementation(project(":portaro-auth-bankid"))
    implementation(project(":portaro-auth-bankid-notify"))
    implementation(project(":portaro-auth-cas"))
    implementation(project(":portaro-auth-credreg"))
    implementation(project(":portaro-auth-ezak-config"))
    implementation(project(":portaro-auth-httpbasic"))
    implementation(project(":portaro-auth-internal"))
    implementation(project(":portaro-auth-ldap"))
    implementation(project(":portaro-auth-logout"))
    implementation(project(":portaro-auth-mojeid"))
    implementation(project(":portaro-auth-multifactor"))
    implementation(project(":portaro-auth-oauth2-authorizationcode"))
    implementation(project(":portaro-auth-oauth2-authorizationserver"))
    implementation(project(":portaro-auth-pairing"))
    implementation(project(":portaro-auth-pairing-config"))
    implementation(project(":portaro-auth-jwt"))
    implementation(project(":portaro-auth-saml2-idp"))
    implementation(project(":portaro-auth-saml2-sp"))
    implementation(project(":portaro-auth-sidechannel"))
    implementation(project(":portaro-auth-stringpair"))
    implementation(project(":portaro-auth-switchuser"))
    implementation(project(":portaro-auth-token-config"))
    implementation(project(":portaro-auth-useragent"))
    implementation(project(":portaro-batch"))
    implementation(project(":portaro-calendar"))
    implementation(project(":portaro-calendar-config"))
    implementation(project(":portaro-catalog"))
    implementation(project(":portaro-catalog-web"))
    implementation(project(":portaro-comment"))
    implementation(project(":portaro-commons"))
    implementation(project(":portaro-commons-image"))
    implementation(project(":portaro-commons-pdf"))
    implementation(project(":portaro-commons-crypto"))
    implementation(project(":portaro-core"))
    implementation(project(":portaro-core-template-velocity"))
    implementation(project(":portaro-database-backup"))
    implementation(project(":portaro-database-datacopy"))
    implementation(project(":portaro-database-properties"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-erp"))
    implementation(project(":portaro-exemplar"))
    implementation(project(":portaro-exemplar-export"))
    implementation(project(":portaro-exemplar-import"))
    implementation(project(":portaro-export"))
    implementation(project(":portaro-ext-alive"))
    implementation(project(":portaro-ext-bakalari"))
    implementation(project(":portaro-ext-cpk"))
    implementation(project(":portaro-ext-cpk-impl"))
    implementation(project(":portaro-ext-edookit"))
    implementation(project(":portaro-ext-edupage"))
    implementation(project(":portaro-ext-ifis"))
    implementation(project(":portaro-ext-obalkyknih"))
    implementation(project(":portaro-ext-powerkey"))
    implementation(project(":portaro-ext-sutin"))
    implementation(project(":portaro-ext-sol"))
    implementation(project(":portaro-ext-unis"))
    implementation(project(":portaro-ext-unob"))
    implementation(project(":portaro-ext-synchronizer"))
    implementation(project(":portaro-ext-report-server"))
    implementation(project(":portaro-ext-wallet"))
    implementation(project(":portaro-ext-ziskej"))
    implementation(project(":portaro-ext-ziskej-impl"))
    implementation(project(":portaro-finance"))
    implementation(project(":portaro-finance-config"))
    implementation(project(":portaro-file"))
    implementation(project(":portaro-file-config"))
    implementation(project(":portaro-file-image"))
    implementation(project(":portaro-file-text"))
    implementation(project(":portaro-file-text-config"))
    implementation(project(":portaro-form"))
    implementation(project(":portaro-form-annotation"))
    implementation(project(":portaro-form-config"))
    implementation(project(":portaro-integ-feign"))
    implementation(project(":portaro-inventory"))
    implementation(project(":portaro-licence"))
    implementation(project(":portaro-licence-updater"))
    implementation(project(":portaro-loan"))
    implementation(project(":portaro-loan-config"))
    implementation(project(":portaro-loan-ill"))
    implementation(project(":portaro-message"))
    implementation(project(":portaro-marcxml"))
    implementation(project(":portaro-ncip"))
    implementation(project(":portaro-ncip-impl"))
    implementation(project(":portaro-oai-provider"))
    implementation(project(":portaro-oai-provider-impl"))
    implementation(project(":portaro-payment"))
    implementation(project(":portaro-payment-provider"))
    implementation(project(":portaro-payment-provider-csobgw"))
    implementation(project(":portaro-payment-provider-gopay"))
    implementation(project(":portaro-payment-provider-gpwebpay"))
    implementation(project(":portaro-payment-provider-manual"))
    implementation(project(":portaro-pops"))
    implementation(project(":portaro-proveniences-map"))
    implementation(project(":portaro-record"))
    implementation(project(":portaro-record-binding"))
    implementation(project(":portaro-record-citation"))
    implementation(project(":portaro-record-collection"))
    implementation(project(":portaro-record-config"))
    implementation(project(":portaro-record-export"))
    implementation(project(":portaro-record-grid-config"))
    implementation(project(":portaro-record-import"))
    implementation(project(":portaro-resources-update"))
    implementation(project(":portaro-sba"))
    implementation(project(":portaro-scheduler"))
    implementation(project(":portaro-sdi"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-search-factory"))
    implementation(project(":portaro-search-ui"))
    implementation(project(":portaro-search-z"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-sip2-server-impl"))
    implementation(project(":portaro-sql-generator"))
    implementation(project(":portaro-thumbnail-config"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-user-impl"))
    implementation(project(":portaro-user-config"))
    implementation(project(":portaro-user-preferences"))
    implementation(project(":portaro-util"))
    implementation(project(":portaro-verbisbox"))
    implementation(project(":portaro-verbisboxer-manager"))
    implementation(project(":portaro-web"))
    implementation(project(":portaro-web-proxy"))

    implementation("org.springframework:spring-context-support:6.+")
    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework:spring-webmvc:6.+")
    implementation("org.springframework:spring-webflux:6.+") // to temporary fix missing (because of older version) org.springframework.web.reactive.function.client.ClientRequestObservationConvention dependecy in org.springframework.boot.actuate.autoconfigure.observation.web.client.WebClientObservationConfiguration
    implementation("org.springframework.security:spring-security-cas:6.+")
    implementation("org.springframework.security:spring-security-config:6.+")
    implementation("org.springframework.security:spring-security-web:6.+")
    implementation("org.springframework.security:spring-security-openid:5.+")
    implementation("org.springframework.boot:spring-boot-starter-logging:3.+") //to enable logback (with slf4j-api) and adapters (jcl-over-slf4j, jul-to-slf4j and log4j-over-slf4j)
    implementation("org.springframework.boot:spring-boot-starter-data-jpa:3.+")
    implementation("org.springframework.boot:spring-boot-starter-validation:3.+")
    implementation("org.springframework.boot:spring-boot-starter-thymeleaf:3.+") // to force org.thymeleaf:thymeleaf-spring6 (db-scheduler-ui has dependency on spring-boot-starter-thymeleaf:2.7.15)
    implementation("org.springframework.boot:spring-boot-starter-web:3.+") //spring boot embeded server
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign:4.+")

    api("org.jspecify:jspecify:+")
    implementation("com.twelvemonkeys.servlet:servlet:3.11.0:jakarta")
    implementation("org.freemarker:freemarker:2.+")
    implementation("org.hibernate.orm:hibernate-core:6.5.+")
    implementation("org.hibernate.orm:hibernate-community-dialects:6.5.+")
    implementation("org.hibernate.validator:hibernate-validator:6.2.+")
    implementation("com.zaxxer:HikariCP:6.+")
    implementation("org.firebirdsql.jdbc:jaybird:6.0.1") // do not migrate to 6.0.2 - there is bug in users by id loading (in unob users sync) (java.lang.NegativeArraySizeException: -4508 in XdrInputStream.readRawBuffer(XdrInputStream.java:147); suppressed: Unsupported or unexpected operation code 352583681 in processOperation [SQLState:08000, ISC error code:337248276])
    implementation("org.postgresql:postgresql:42.7.7")
    implementation("com.h2database:h2:+")
    implementation("org.jcommander:jcommander:2.0")
    implementation("javax.cache:cache-api:+")
    implementation("jakarta.mail:jakarta.mail-api:2.+")
    implementation("org.eclipse.angus:jakarta.mail:1.+") // this is implementation of jakarta.mail:jakarta.mail-api
    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("jakarta.xml.bind:jakarta.xml.bind-api:4.0.+")
    implementation("de.codecentric:spring-boot-admin-starter-client:3.+")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.+") //jdk8 json features (support for Optional and so on) - remove after upgrade to jackson 3
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.+")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-csv:2.+")
    implementation("org.glassfish.jaxb:jaxb-runtime:4.0.+") // this is impl, api is on javax.xml.bind:jaxb-api:2+. We can use com.sun.xml.bind:jaxb-impl or org.glassfish.jaxb:jaxb-runtime, but spring supports second one (https://github.com/spring-projects/spring-ws/issues/1305).
    implementation("com.github.ben-manes.caffeine:caffeine:3.+")
    implementation("com.google.guava:guava:+")
    implementation("org.beryx:text-io:+")
    implementation("org.apache.velocity:velocity-engine-core:2.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("com.github.kagkarlsson:db-scheduler-spring-boot-starter:+")
    implementation("no.bekk.db-scheduler-ui:db-scheduler-ui-starter:+")
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:+")
    implementation("com.github.ulisesbocchio:jasypt-spring-boot-starter:+")
}

val env: String = ext.get("env") as String
val projectVersion: String = ext.get("version") as String
val versionMajorMinor: String = ext.get("versionMajorMinor") as String
val isLatestGaMajorMinorVersion: Boolean = ext.get("isLatestGaMajorMinorVersion") as Boolean
val isTested: Boolean = ext.get("isTested") as Boolean
val frontendDirectory = file("${layout.projectDirectory}/src/main/resources/resources")
val runnerMainClass = "cz.kpsys.portaro.PortaroApplication"
val ftpUsername = project.findProperty("ftpUsername") as String?
val ftpPassword = project.findProperty("ftpPassword") as String?
val dockerUsername = project.findProperty("dockerUsername") as String?
val dockerPassword = project.findProperty("dockerPassword") as String?

// Nasty hack until https://github.com/n0mer/gradle-git-properties/issues/242 is fixed
fun resolveGitDir(): Directory {
    val git = layout.settingsDirectory.dir(".git").asFile.toPath()
    if (git.isRegularFile()) { // is Git worktree
        val content: String = git.readText()
        val worktreePath = content.split(": ")[1]
        return layout.settingsDirectory.dir(Paths.get(worktreePath).parent.parent.toString())
    } else {
        return layout.settingsDirectory.dir(".git") // temporary workaround until https://github.com/n0mer/gradle-git-properties/issues/240 is fixed
    }
}

gitProperties {
    dateFormat = "yyyy-MM-dd'T'HH:mmZ"
    dotGitDirectory = resolveGitDir()
}

springBoot {
    mainClass = runnerMainClass
    buildInfo()
}

tasks.bootRun {
    // support passing -Dsystem.property=value to bootRun task
    systemProperties = (System.getProperties() as Map<*, *>)
        .mapKeys { it.key.toString() }
        .filterKeys { listOf("appserver.url", "server.port", "database.host", "database.port", "database.file", "portaro.projectroot").contains(it) }
        .filterValues { it != "" }
}

node {
    version = "20.11.1"
    npmVersion = "10.2.4"
    download = ! System.getProperty("useLocalNodejs", "false").equals("true")
    nodeProjectDir = frontendDirectory
    distBaseUrl = System.getProperty("nodejsRepositoryUrl")
    allowInsecureProtocol = true
}

tasks.processResources {
    dependsOn("buildFrontend")

    exclude("**/docs")
    exclude("**/test")
    exclude("**/node_modules")
    exclude("**/webpack**")
    exclude("**/package.json")
    exclude("**/package-lock.json")
    exclude("**/.babelrc")
    exclude("**/stats.json")
    exclude { ftl ->
        ftl.file.path.contains("resources/src") &&
                !(ftl.file.name.endsWith(".ftl")) &&
                !ftl.isDirectory
    }
}

tasks.withType<NodeSetupTask> {
    outputs.cacheIf { false }
}

tasks.withType<NpmSetupTask> {
    outputs.cacheIf { false }
}

tasks.register<NpmTask>("npmCi") {
    dependsOn("npmSetup")
    npmCommand = listOf("ci")
    inputs.file(file("${frontendDirectory}/package.json"))
    inputs.file(file("${frontendDirectory}/package-lock.json"))
    outputs.dir(file("${frontendDirectory}/node_modules"))
    // https://stackoverflow.com/questions/29323982/error-cannot-find-module-lib-cli
    outputs.cacheIf { false }
}

tasks.register<NpmTask>("buildFrontend") {
    dependsOn("npmCi")

    group = "build"
    description = "Run frontend build with development or production configuration"
    workingDir = frontendDirectory

    defineCacheInputsForFrontEnd(inputs);

    outputs.dir(file("${frontendDirectory}/dist"))
    outputs.cacheIf { true }

    if (project.hasProperty("karmaServerHostIp")) {
        environment.put("karmaServerHostIp", project.property("karmaServerHostIp") as String)
    }

    if (project.hasProperty("chromiumBin")) {
        environment.put("CHROMIUM_BIN", project.property("chromiumBin") as String)
    }

    val buildTarget: String = "build" +
            if (env == "prod") ":prod" +
                    (if (ext.has("withoutFETests")) "-without-tests" else "")
            else ""

    args = listOf("run", buildTarget)
}

tasks.register<NpmTask>("testFrontend") {
    dependsOn("npmCi")
    group = "verification"
    description = "Run front-end tests"

    workingDir = frontendDirectory
    args = listOf("test")
}

tasks.register<NpmTask>("qualityReport") {
    dependsOn("npmCi")
    group = "reports"
    description = "Create code quality report"

    workingDir = frontendDirectory
    args = listOf("run", "quality")
}

tasks.register("publishJar") {
    dependsOn("bootJar")
    group = "publish"
    description = "Publish portaro jar to kpsys ftp server"

    doLast {
        val jarFile = file("${layout.buildDirectory.get()}/libs/portaro-runner-${projectVersion}.jar")
        val pathsForFiles: MutableSet<String> = mutableSetOf("/portaro-${versionMajorMinor}-latest.jar")
        if (isLatestGaMajorMinorVersion) {
            pathsForFiles += "/portaro-latest.jar"
        }
        uploadToFtp(
            jarFile,
            pathsForFiles,
            "ftp.kpsys.cz",
            "/portaro",
            ftpUsername ?: error("missing ftpUsername property"),
            ftpPassword ?: error("missing ftpPassword property")
        )
    }
}

// remove after https://github.com/GoogleContainerTools/jib/issues/3132
tasks.withType<JibTask> {
    notCompatibleWithConfigurationCache("because https://github.com/GoogleContainerTools/jib/issues/3132")
}

jib {
    from {
        image = "docker.kpsys.cz/kpsys/portaro-parent"
    }
    to {
        val imageName = "docker-push.kpsys.cz/kpsys/portaro"
        val tagSuffix = if (env == "prod") "" else "-${env}"

        if (isTested) {
            image = "${imageName}:${versionMajorMinor}${tagSuffix}"
            if (isLatestGaMajorMinorVersion) {
                tags = when (env) {
                    "prod" -> setOf("latest")
                    "develop" -> setOf("develop")
                    "preview" -> setOf("preview")
                    "local" -> setOf("local")
                    else -> setOf()
                }
            }
        } else {
            image = "${imageName}:${projectVersion}${tagSuffix}"
        }

        auth {
            username = dockerUsername
            password = dockerPassword
        }
    }
    container {
        ports = listOf("80")
        mainClass = runnerMainClass
        creationTime = "USE_CURRENT_TIMESTAMP"
    }
}
