package cz.kpsys.portaro.auth.view;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.current.AuthableUserResponse;
import cz.kpsys.portaro.auth.current.CurrentAuthResponse;
import cz.kpsys.portaro.auth.department.CurrentAuthDepartmentsLoader;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.HierarchicalDepartment;
import cz.kpsys.portaro.user.AuthableUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CurrentAuthToResponseConverter implements Converter<UserAuthentication, CurrentAuthResponse> {

    @NonNull CurrentAuthDepartmentsLoader currentAuthReadableDepartmentsLoader;
    @NonNull CurrentAuthDepartmentsLoader currentAuthEditableDepartmentsLoader;
    @NonNull Converter<AuthableUser, AuthableUserResponse> authableUserToResponseConverter;

    @Override
    public CurrentAuthResponse convert(@NonNull UserAuthentication currentAuth) {
        List<Department> editableDepartments = currentAuthEditableDepartmentsLoader.getSubtreesByAuth(currentAuth);
        List<Department> readableDepartments = currentAuthReadableDepartmentsLoader.getSubtreesByAuth(currentAuth);

        AuthableUserResponse activeUserResponse = Objects.requireNonNull(authableUserToResponseConverter.convert(currentAuth.getActiveUser()));
        return new CurrentAuthResponse(
                currentAuth.isEvided(),
                activeUserResponse,
                currentAuth.getRole(),
                readableDepartments,
                editableDepartments,
                convertToHierarchy(readableDepartments)
        );
    }

    private List<HierarchicalDepartment> convertToHierarchy(List<Department> readableDepartments) {
        if (readableDepartments.isEmpty()) {
            return List.of();
        }

        Set<Integer> readableDepartmentIds = readableDepartments.stream()
                .map(Department::getId)
                .collect(Collectors.toSet());

        List<Department> rootDepartments = new ArrayList<>();
        List<Department> childDepartments = new ArrayList<>();

        for (Department department : readableDepartments) {
            // A department is considered root if:
            // It has no parentId (parentId == null), OR
            // Its parent is not in the readable departments list
            if (department.getParentId() == null || !readableDepartmentIds.contains(department.getParentId())) {
                rootDepartments.add(department);
            } else {
                childDepartments.add(department);
            }
        }

        rootDepartments.sort(Department.COMPARATOR);

        return rootDepartments.stream()
                .map(rootDepartment -> buildHierarchicalDepartment(rootDepartment, childDepartments))
                .toList();
    }

    private HierarchicalDepartment buildHierarchicalDepartment(Department department, List<Department> allChildDepartments) {
        List<Department> directChildren = new ArrayList<>();
        List<Department> otherDepartments = new ArrayList<>();

        for (Department childDepartment : allChildDepartments) {
            if (ObjectUtil.nullSafeEquals(childDepartment.getParentId(), department.getId())) {
                directChildren.add(childDepartment);
            } else {
                otherDepartments.add(childDepartment);
            }
        }

        directChildren.sort(Department.COMPARATOR);

        List<HierarchicalDepartment> hierarchicalChildren = directChildren.stream()
                .map(child -> buildHierarchicalDepartment(child, otherDepartments))
                .toList();

        return new HierarchicalDepartment(department, hierarchicalChildren);
    }
}
