package cz.kpsys.portaro.messages.dto;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import lombok.NonNull;

public record NewThreadMessageCommand(

        @NonNull
        Record thread,

        @NonNull
        String content,

        boolean withFiles,

        @NonNull
        UserAuthentication currentAuth,

        @NonNull
        Department ctx
) {
}
