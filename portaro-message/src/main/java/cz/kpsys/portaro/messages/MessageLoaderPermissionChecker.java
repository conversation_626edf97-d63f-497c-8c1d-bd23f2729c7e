package cz.kpsys.portaro.messages;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.function.TriConsumer;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.messages.constants.MessageConstants;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MessageLoaderPermissionChecker implements TriConsumer<MapBackedParams, UserAuthentication, Department> {

    @NonNull SecurityManager securityManager;

    @Override
    public void accept(MapBackedParams mapBackedParams, UserAuthentication currentAuth, Department ctx) {
        if (mapBackedParams.hasNotNull(MessageConstants.SearchParams.THREAD)) {
            securityManager.throwIfCannot(MessageSecurityActions.READ_THREAD, currentAuth, ctx, mapBackedParams.get(MessageConstants.SearchParams.THREAD));
        } else {
            securityManager.throwIfCannot(MessageSecurityActions.MESSAGE_SEARCH, currentAuth, ctx, null);
        }
    }
}
