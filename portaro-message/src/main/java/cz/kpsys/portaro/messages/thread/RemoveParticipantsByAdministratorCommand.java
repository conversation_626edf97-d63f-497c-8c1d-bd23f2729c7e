package cz.kpsys.portaro.messages.thread;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;

import java.util.List;

public record RemoveParticipantsByAdministratorCommand(

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth,

        @NonNull
        Record thread,

        @NonNull
        List<@NonNull BasicUser> participants
) implements ThreadParticipantRemovalCommand {
}
