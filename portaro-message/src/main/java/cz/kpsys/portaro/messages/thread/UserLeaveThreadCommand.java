package cz.kpsys.portaro.messages.thread;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;

import java.util.Collection;
import java.util.List;

public record UserLeaveThreadCommand(

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth,

        @NonNull
        Record thread
) implements ThreadParticipantRemovalCommand {

    @Override
    public @NonNull Collection<BasicUser> participants() {
        return List.of(currentAuth.getActiveUser());
    }
}
