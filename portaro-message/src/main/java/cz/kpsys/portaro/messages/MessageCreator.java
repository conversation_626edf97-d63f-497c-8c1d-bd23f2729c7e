package cz.kpsys.portaro.messages;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.file.FileAccessType;
import cz.kpsys.portaro.file.directory.ParentableDirectoryCreateCommand;
import cz.kpsys.portaro.file.directory.ParentableDirectoryCreator;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.messages.constants.MessageSeverity;
import cz.kpsys.portaro.messages.constants.MessageTopic;
import cz.kpsys.portaro.messages.dto.*;
import cz.kpsys.portaro.messages.thread.ThreadMentionSetter;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MessageCreator {

    @NonNull Saver<Message, ?> messageSaver;
    @NonNull Saver<MessageSending, ?> messageSendingSaver;
    @NonNull Saver<List<MessageMention>, ?> messageMentionSaver;
    @NonNull Eventer eventer;
    @NonNull TransactionTemplate readwriteTransactionTemplate;
    @NonNull MessagePublisher messagePublisher;
    @NonNull ParentableDirectoryCreator parentableDirectoryCreator;
    @NonNull ByIdLoadable<FileAccessType, Integer> fileAccessTypeLoader;
    @NonNull ThreadMentionSetter threadMentionSetter;
    @NonNull MessageMentionsExtractor messageMentionsExtractor;
    @NonNull SecurityManager securityManager;

    public Message createInternal(@NonNull NewThreadMessageCommand command) {
        securityManager.throwIfCannot(MessageSecurityActions.READ_THREAD, command.currentAuth(), command.ctx(), command.thread().getId());
        return readwriteTransactionTemplate.execute(_ -> {
            UUID messageId = UuidGenerator.forIdentifier();
            Integer directoryId = getDirectoryId(messageId, command);

            UserToThreadMessage message = Message.ofUserToThread(
                    messageId,
                    command.content(),
                    MessageTopic.COMMON,
                    MessageSeverity.INFO,
                    command.currentAuth().getActiveUser(),
                    command.thread(),
                    command.ctx(),
                    false,
                    ContentType.MARKDOWN,
                    directoryId,
                    Instant.now(),
                    null
            );

            Event sendingEvent = eventer.save(Event.Codes.MESSAGE_SENDING, command.currentAuth(), command.ctx(), null);

            MessageSendingInternal messageSending = new MessageSendingInternal(
                    message.id(),
                    message.id(),
                    sendingEvent,
                    null,
                    null,
                    command.content()
            );

            List<MessageMention> messageMentions = messageMentionsExtractor.extractMentions(command.content()).stream().map(record -> new MessageMention(UuidGenerator.forIdentifier(), message, record.getId())).toList();

            messageSaver.save(message);
            messageSendingSaver.save(messageSending);
            messageMentionSaver.save(messageMentions);

            if (!messageMentions.isEmpty()) {
                threadMentionSetter.addAsMentionIfNotPresent(command.thread(), messageMentions);
            }

            if (!command.withFiles()) {
                messagePublisher.publish(message, messageMentions);
            }

            return message;
        });
    }

    private @Nullable Integer getDirectoryId(@NonNull UUID messageId, @NonNull NewThreadMessageCommand command) {
        if (command.withFiles()) {
            return parentableDirectoryCreator.create(ParentableDirectoryCreateCommand.ofNewDirectory(command.thread().getDirectoryId(), messageId.toString(), fileAccessTypeLoader.getById(FileAccessType.THREAD_PARTICIPANT_ONLY))).getId();
        }
        return null;
    }
}
