package cz.kpsys.portaro.messages.thread;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.NonNull;

import java.util.List;
import java.util.UUID;

public record AddParticipantsByAdministratorRequest(

        @Schema(implementation = UUID.class, description = "Thread")
        @NotNull
        Record thread,

        @ArraySchema(schema = @Schema(implementation = Integer.class, example = User.SCHEMA_EXAMPLE_PERSON_ID, description = "New thread participant user id"))
        @NotNull
        List<@NotNull BasicUser> participants
) {

    public AddParticipantsByAdministratorCommand toCommand(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        return new AddParticipantsByAdministratorCommand(ctx, currentAuth, thread(), participants());
    }
}
