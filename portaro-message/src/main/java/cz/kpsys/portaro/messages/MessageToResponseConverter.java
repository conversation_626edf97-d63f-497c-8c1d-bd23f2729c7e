package cz.kpsys.portaro.messages;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.FileSearchParams;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.messages.dto.Message;
import cz.kpsys.portaro.messages.dto.UserToThreadMessage;
import cz.kpsys.portaro.messages.dto.UserToUserMessage;
import cz.kpsys.portaro.record.LabeledRecordRef;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class MessageToResponseConverter implements ViewableItemsTypedConverter<Message, MessageResponse> {

    @NonNull MessageMentionsExtractor messageMentionsExtractor;
    @NonNull ParameterizedSearchLoader<MapBackedParams, IdentifiedFile> fileSearchLoader;

    @Override
    public @NonNull List<MessageResponse> convert(@NonNull List<Message> messages, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale, @NonNull List<String> exports, @NonNull Map<String, Object> additionalModel) {
        Map<Message, List<Record>> messageMentions = messageMentionsExtractor.extractMentions(messages);
        Map<Message, List<IdentifiedFile>> messagesAttachments = batchLoadMessagesAttachments(messages);

        return messages.stream()
                .map(message -> convertSingle(message, convertRecordToLabeledRecordRef(messageMentions.getOrDefault(message, List.of())), messagesAttachments.getOrDefault(message, List.of())))
                .toList();
    }

    @Override
    public @NonNull MessageResponse convertSingle(@NonNull Message message, @NonNull UserAuthentication currentAuth, @NonNull Department ctx, @NonNull Locale locale, @NonNull List<String> exports, @NonNull Map<String, Object> additionalModel) {
        return convertSingle(message);
    }

    public @NonNull MessageResponse convertSingle(@NonNull Message message) {
        List<LabeledRecordRef> mentions = convertRecordToLabeledRecordRef(messageMentionsExtractor.extractMentions(message.content()));
        List<IdentifiedFile> attachments = batchLoadMessagesAttachments(List.of(message)).getOrDefault(message, List.of());

        return convertSingle(message, mentions, attachments);
    }

    private @NonNull MessageResponse convertSingle(@NonNull Message message, List<LabeledRecordRef> mentions, List<IdentifiedFile> attachments) {

        return switch (message) {
            case UserToThreadMessage userToThreadMessage -> new MessageResponse(
                    userToThreadMessage.id(),
                    userToThreadMessage.content(),
                    userToThreadMessage.topic(),
                    userToThreadMessage.severity(),
                    userToThreadMessage.senderUser(),
                    userToThreadMessage.thread(),
                    null, // targetUser is null for thread messages
                    userToThreadMessage.contentType(),
                    userToThreadMessage.directoryId(),
                    userToThreadMessage.creationDate(),
                    userToThreadMessage.activationDate(),
                    mentions,
                    attachments
            );
            case UserToUserMessage userToUserMessage -> new MessageResponse(
                    userToUserMessage.id(),
                    userToUserMessage.content(),
                    userToUserMessage.topic(),
                    userToUserMessage.severity(),
                    userToUserMessage.senderUser(),
                    null, // thread is null for user-to-user messages
                    userToUserMessage.targetUser(),
                    userToUserMessage.contentType(),
                    userToUserMessage.directoryId(),
                    userToUserMessage.creationDate(),
                    userToUserMessage.activationDate(),
                    mentions,
                    attachments
            );
        };
    }

    private Map<Message, List<IdentifiedFile>> batchLoadMessagesAttachments(@NonNull List<Message> messages) {
        Map<Integer, Message> messageByDirectoryId = messages.stream()
                .filter(m -> m.directoryId() != null)
                .collect(Collectors.toMap(Message::directoryId, Function.identity()));

        List<Integer> directoryIds = new ArrayList<>(messageByDirectoryId.keySet());

        MapBackedParams params = MapBackedParams.build(StaticParamsModifier.of(
                FileSearchParams.DIRECTORY, directoryIds)
        );

        return fileSearchLoader.getPage(RangePaging.forAll(), params).getItems().stream()
                .collect(Collectors.groupingBy(
                        file -> messageByDirectoryId.get(file.getDirectory().getId())
                ));
    }

    private static @NonNull List<LabeledRecordRef> convertRecordToLabeledRecordRef(List<Record> records) {
        return records
                .stream().map((record) -> LabeledRecordRef.ofRecordLink(record.idFondPair(), record.getText())).toList();
    }
}