package cz.kpsys.portaro.messages.thread;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.messages.*;
import cz.kpsys.portaro.messages.request.NewThreadMessageRequest;
import cz.kpsys.portaro.messages.request.PublishMessageRequest;
import cz.kpsys.portaro.messages.request.UserReadAllThreadMessagesRequest;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.web.GenericApiController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Locale;

import static cz.kpsys.portaro.messages.constants.MessageConstants.*;

@Tag(name = "thread", description = "Endpoints for managing threads")
@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ThreadApiController extends GenericApiController {

    @NonNull ThreadCreator threadCreator;
    @NonNull MessageCreator messageCreator;
    @NonNull ThreadLoader threadLoader;
    @NonNull ThreadUpdater threadUpdater;
    @NonNull Converter<Record, ThreadResponse> threadToResponseConverter;
    @NonNull UserThreadsLoader userThreadsLoader;
    @NonNull UserThreadInfoToResponseConverter userThreadInfoToResponseConverter;
    @NonNull MessagePublisher messagePublisher;
    @NonNull MessageToResponseConverter messageToResponseConverter;

    @Operation(summary = "Get main thread by linked record")
    @GetMapping(THREAD_BY_RECORD_URL)
    public ThreadResponse getThreadByRecord(@PathVariable("recordId") Record record) {
        return threadToResponseConverter.convert(threadLoader.getRecordThread(record));
    }

    @Operation(summary = "Get thread by thread id")
    @GetMapping(THREAD_BY_ID_URL)
    public ThreadResponse getThread(@PathVariable("id") Record thread) {
        return threadToResponseConverter.convert(thread);
    }

    @Operation(summary = "Get all thread where is user as participants")
    @GetMapping(USER_THREADS_URL)
    public List<UserThreadInfoResponse> getUserThreads(@PathVariable("user") BasicUser user) {
        return ListUtil.convert(userThreadsLoader.getThreads(user), userThreadInfoToResponseConverter);
    }

    @Operation(summary = "Create new thread")
    @PostMapping(THREADS_CREATE_URL)
    public ThreadResponse create(@RequestBody @ValidFormObject NewThreadRequest request, @CurrentDepartment Department ctx, UserAuthentication currentAuth) {
        Record record = threadCreator.create(request.toCommand(ctx, currentAuth));
        return threadToResponseConverter.convert(record);
    }

    @Operation(summary = "User leaves thread")
    @PostMapping(THREADS_LEAVE_URL)
    public ThreadResponse leaveThread(@RequestBody @ValidFormObject UserLeaveThreadRequest request, @CurrentDepartment Department ctx, UserAuthentication currentAuth) {
        threadUpdater.removeParticipants(request.toCommand(ctx, currentAuth));
        return threadToResponseConverter.convert(request.thread());
    }

    @Operation(summary = "User joins thread")
    @PostMapping(THREADS_JOIN_URL)
    public ThreadResponse joinThread(@RequestBody @ValidFormObject UserJoinThreadRequest request, @CurrentDepartment Department ctx, UserAuthentication currentAuth) {
        threadUpdater.addParticipants(request.toCommand(ctx, currentAuth));
        return threadToResponseConverter.convert(request.thread());
    }

    @Operation(summary = "Add participant to thread")
    @PostMapping(THREADS_ADD_PARTICIPANTS_URL)
    public ThreadResponse addParticipants(@RequestBody @ValidFormObject AddParticipantsByAdministratorRequest request, @CurrentDepartment Department ctx, UserAuthentication currentAuth) {
        threadUpdater.addParticipants(request.toCommand(ctx, currentAuth));
        return threadToResponseConverter.convert(request.thread());
    }

    @Operation(summary = "Remove participant to thread")
    @PostMapping(THREADS_REMOVE_PARTICIPANTS_URL)
    public ThreadResponse removeParticipants(@RequestBody @ValidFormObject RemoveParticipantsByAdministratorRequest request, @CurrentDepartment Department ctx, UserAuthentication currentAuth) {
        threadUpdater.removeParticipants(request.toCommand(ctx, currentAuth));
        return threadToResponseConverter.convert(request.thread());
    }

    // TODO ještě se musí dodělat security s tím že upravit název toho threadu budou moc všichni participanty teď to nejde kvůli tomuže se upravuje ten record.
    @Operation(summary = "Update thread information")
    @PostMapping(THREADS_UPDATE_URL)
    public ThreadResponse updateThread(@RequestBody @ValidFormObject UpdateThreadRequest request, @CurrentDepartment Department ctx, UserAuthentication currentAuth) {
        Record record = threadUpdater.updateThread(request.toCommand(ctx, currentAuth));
        return threadToResponseConverter.convert(record);
    }

    @Operation(summary = "Send message to thread")
    @PostMapping(THREADS_SEND_MESSAGE_URL)
    public MessageResponse newThreadMessage(@RequestBody @ValidFormObject NewThreadMessageRequest message, @CurrentDepartment Department ctx, UserAuthentication currentAuth, Locale locale) {
        return messageToResponseConverter.convertSingle(messageCreator.createInternal(message.toCommand(ctx, currentAuth)), currentAuth, ctx, locale);
    }

    @Operation(summary = "Publish message")
    @PostMapping(THREADS_PUBLISH_MESSAGE_URL)
    public MessageResponse publish(@RequestBody @ValidFormObject PublishMessageRequest request, @CurrentDepartment Department ctx, UserAuthentication currentAuth, Locale locale) {
        return messageToResponseConverter.convertSingle(messagePublisher.publish(request.message(), List.of()), currentAuth, ctx, locale);
    }

    @Operation(summary = "User has read all thread messages.")
    @PostMapping(USER_READ_ALL_THREAD_MESSAGES_URL)
    public void userReadAllThreadMessages(@RequestBody @ValidFormObject UserReadAllThreadMessagesRequest request, @CurrentDepartment Department ctx, UserAuthentication currentAuth) {
        threadUpdater.userReadAllThreadMessages(request.toCommand(ctx, currentAuth));
    }
}
