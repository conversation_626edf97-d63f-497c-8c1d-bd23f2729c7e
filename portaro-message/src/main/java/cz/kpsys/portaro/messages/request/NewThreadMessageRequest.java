package cz.kpsys.portaro.messages.request;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.messages.dto.NewThreadMessageCommand;
import cz.kpsys.portaro.record.Record;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.util.UUID;

public record NewThreadMessageRequest(

        @Schema(implementation = UUID.class, description = "Thread UUID", example = Record.SCHEMA_EXAMPLE_DOCUMENT_ID)
        @NotNull
        Record thread,

        @NotNull
        String content,

        boolean withFiles

) {
    public NewThreadMessageCommand toCommand(Department ctx, UserAuthentication currentAuth) {
        return new NewThreadMessageCommand(thread(), content(), withFiles(), currentAuth, ctx);
    }
}
