package cz.kpsys.portaro.messages.thread;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;

import java.util.Collection;

public sealed interface ThreadParticipantAdditionCommand
        permits AddParticipantsByAdministratorCommand, UserJoinThreadCommand {

    @NonNull
    Department ctx();

    @NonNull
    UserAuthentication currentAuth();

    @NonNull
    Record thread();

    @NonNull
    Collection<BasicUser> participants();
}