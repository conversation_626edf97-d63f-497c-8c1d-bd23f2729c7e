package cz.kpsys.portaro.messages.thread;

import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.messages.MessageSecurityActions;
import cz.kpsys.portaro.messages.ThreadNotifier;
import cz.kpsys.portaro.messages.dto.UserReadAllThreadMessagesCommand;
import cz.kpsys.portaro.messages.participants.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import cz.kpsys.portaro.security.Action;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Instant;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ThreadUpdater {

    @NonNull ThreadParticipantSaver threadParticipantSaver;
    @NonNull ThreadParticipantRemover threadParticipantRemover;
    @NonNull ThreadParticipantLoader threadParticipantLoader;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull TransactionTemplate transactionTemplate;
    @NonNull SecurityManager securityManager;
    @NonNull ThreadNotifier threadNotifier;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;

    public void addParticipants(@NonNull ThreadParticipantAdditionCommand command) {
        Action<UUID> action = switch (command) {
            case AddParticipantsByAdministratorCommand _ -> MessageSecurityActions.EDIT_THREAD;
            case UserJoinThreadCommand _ -> MessageSecurityActions.JOIN_THREAD;
        };
        securityManager.throwIfCannot(action, command.currentAuth(), command.ctx(), command.thread().getId());
        transactionTemplate.executeWithoutResult(_ -> {
            for (BasicUser user : command.participants()) {
                Optional<ThreadParticipant> participantOrMention =
                        threadParticipantLoader.getOptionalParticipantOrMention(command.thread(), user);

                if (participantOrMention.isEmpty()) {
                    threadParticipantSaver.save(ThreadParticipant.participant(
                            UuidGenerator.forIdentifier(),
                            command.thread().getId(),
                            user.getRid(),
                            Instant.now(),
                            null,
                            false
                    ));
                } else {
                    threadParticipantSaver.updateToParticipant(participantOrMention.get());
                }

                threadNotifier.participantAdded(command.thread(), user, command.currentAuth());
            }
        });
    }

    public void removeParticipants(@NonNull ThreadParticipantRemovalCommand command) {
        Action<UUID> action = switch (command) {
            case RemoveParticipantsByAdministratorCommand _ -> MessageSecurityActions.EDIT_THREAD;
            case UserLeaveThreadCommand _ -> MessageSecurityActions.LEAVE_THREAD;
        };
        securityManager.throwIfCannot(action, command.currentAuth(), command.ctx(), command.thread().getId());
        transactionTemplate.executeWithoutResult(_ -> {
            for (BasicUser participant : command.participants()) {
                threadParticipantRemover.removeByThreadAndParticipant(command.thread().getId(), participant);
                threadNotifier.participantRemoved(command.thread(), participant, command.currentAuth());
            }
        });
    }

    public void userReadAllThreadMessages(UserReadAllThreadMessagesCommand command) {
        securityManager.throwIfCannot(MessageSecurityActions.READ_THREAD, command.currentAuth(), command.ctx(), command.thread().getId());
        transactionTemplate.executeWithoutResult(_ -> {
            ThreadParticipant threadParticipant = threadParticipantLoader.getParticipantOrMention(command.thread(), command.user());
            ThreadParticipant updatedParticipant = switch (threadParticipant) {
                case MainThreadParticipant _ -> throw new IllegalStateException("Cannot clear unread message for MainThreadParticipant.");
                case MentionThreadParticipant mention -> mention.withFirstUnreadMessageId(null);
                case ParticipantThreadParticipant participant -> participant.withFirstUnreadMessageId(null);
            };
            threadParticipantSaver.save(updatedParticipant);
        });
    }

    public Record updateThread(@NonNull UpdateThreadCommand command) {
        securityManager.throwIfCannot(MessageSecurityActions.EDIT_THREAD, command.currentAuth(), command.ctx(), command.thread().getId());
        return transactionTemplate.execute(_ -> {
            RecordEditation recordEditation = recordEditationFactory
                    .on(command.ctx())
                    .ofExisting(command.thread())
                    .build(command.currentAuth());

            FieldTypeId entryField = recordEntryFieldTypeIdResolver.getEntryNativeSubfield(recordEditation.getFond());

            recordEditationHelper.setStringSubfieldValue(command.name(), true, entryField.existingParent(), true, entryField, recordEditation, command.ctx(), command.currentAuth());

            recordEditation.saveIfModified(command.ctx(), command.currentAuth());

            return recordEditation.getRecord();
        });
    }

}
