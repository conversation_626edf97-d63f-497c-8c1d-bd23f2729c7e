package cz.kpsys.portaro.messages;

import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.messages.dto.Message;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.id.UuidGenerator.UUID_PATTERN_INFIX;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MessageMentionsExtractor {

    private static final Pattern MENTION_PATTERN = Pattern.compile(
            "@(" + UUID_PATTERN_INFIX + ")"
    );

    @NonNull IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;

    public List<Record> extractMentions(String content) {
        return nonDetailedRichRecordLoader.getAllByIds(extractMentionsIds(content));
    }

    public Map<Message, List<Record>> extractMentions(List<Message> messages) {
        List<UUID> allMentionIds = messages.stream()
                .flatMap(message -> extractMentionsIds(message.content()).stream())
                .distinct()
                .toList();

        Map<UUID, Record> recordsMap = nonDetailedRichRecordLoader.getAllByIds(allMentionIds).stream()
                .collect(Collectors.toMap(Record::getId, record -> record));

        return messages.stream()
                .collect(Collectors.toMap(
                        message -> message,
                        message -> {
                            List<UUID> messageMentionIds = extractMentionsIds(message.content());
                            return messageMentionIds.stream()
                                    .map(recordsMap::get)
                                    .filter(Objects::nonNull)
                                    .toList();
                        }
                ));
    }

    private List<UUID> extractMentionsIds(String content) {
        Matcher matcher = MENTION_PATTERN.matcher(content);
        List<UUID> mentions = new ArrayList<>();
        while (matcher.find()) {
            mentions.add(UUID.fromString(matcher.group(1)));
        }

        return mentions;
    }
}
