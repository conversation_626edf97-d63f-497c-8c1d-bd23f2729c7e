package cz.kpsys.portaro.erp.reportreferences;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields;
import cz.kpsys.portaro.erp.ReportLoader;
import cz.kpsys.portaro.erp.reportreferences.web.ReportReferenceReportedResponse;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.search.restriction.FieldTypedSearchFieldParsing;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ReportReferencesReportedLoader extends ReportLoader {

    public ReportReferencesReportedLoader(@NonNull HierarchyLoader<Department> contextHierarchyLoader,
                                          @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchLoader,
                                          @NonNull Provider<@NonNull List<Fond>> reportOptimisationFondsProvider,
                                          @NonNull Provider<@NonNull Fond> optimisationReportFondProvider,
                                          @NonNull Provider<@NonNull List<Fond>> projectBusinessItemFonds) {

        super(contextHierarchyLoader, detailedRecordSearchLoader, reportOptimisationFondsProvider, optimisationReportFondProvider, projectBusinessItemFonds);
    }

    public ReportReferenceReportedResponse load(UUID referenceRecordId, Department ctx) {
        List<Record> records = loadReportRecords(ctx, new Conjunction<>(
                new Term<>(FieldTypedSearchFieldParsing.ofLink(RecordSutinSpecificFields.FondWorkReport.Reference.Main.TYPE_ID).toSearchField(), new Eq(referenceRecordId)))
        );

        int hoursSum = sumNumberFieldValuesHolders(records, RecordSutinSpecificFields.FondWorkReport.Reported.StandardHoursValue.FIELD_FINDER);

        return new ReportReferenceReportedResponse(records.size(), hoursSum);
    }
}
