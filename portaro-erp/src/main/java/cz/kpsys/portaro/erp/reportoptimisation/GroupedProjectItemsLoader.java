package cz.kpsys.portaro.erp.reportoptimisation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields;
import cz.kpsys.portaro.erp.reportoptimisation.web.response.GroupedProjectItemsResponse;
import cz.kpsys.portaro.grid.RecordRow;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.detail.value.LocalDateFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.SimpleFondResponse;
import cz.kpsys.portaro.record.search.restriction.FieldTypedSearchFieldParsing;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class GroupedProjectItemsLoader {

    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchLoader;
    @NonNull ViewableItemsTypedConverter<Record, RecordRow> recordsToRecordRowsConverter;
    @NonNull Provider<@NonNull List<Fond>> projectBusinessItemFonds;

    public GroupedProjectItemsResponse load(UUID projectRecordId, Department ctx, UserAuthentication currentAuth, Locale locale) {
        List<Record> records = detailedRecordSearchLoader.getContent(RangePaging.forAll(), p -> {
            p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
            p.set(CoreSearchParams.FACETS_ENABLED, false);
            p.set(CoreSearchParams.INCLUDE_DRAFT, false);
            p.set(CoreSearchParams.INCLUDE_DELETED, false);
            p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
            p.set(RecordConstants.SearchParams.ROOT_FOND, projectBusinessItemFonds.get());
            p.set(CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.SUBTREE));
            p.set(RecordConstants.SearchParams.RECORD_FIELD_VALUE_RESTRICTION, new Conjunction<>(
                    new Term<>(FieldTypedSearchFieldParsing.ofLink(RecordSutinSpecificFields.ReportedProject.Main.TYPE_ID).toSearchField(), new Eq(projectRecordId))
            ));
        });

        List<SimpleFondResponse> uniqueFonds = ListUtil.removeDuplicates(ListUtil.convert(ListUtil.convert(records, Record::getFond), SimpleFondResponse::mapFromFond));

        List<RecordRow> otherItems = records.stream()
                .filter(record -> RecordSutinSpecificFields.FondInstallationLogbook.FOND_ID != record.getFond().getId())
                .map(record -> recordsToRecordRowsConverter.convertSingle(record, currentAuth, ctx, locale))
                .toList();

        List<Record> installationLogbooks = records.stream()
                .filter(record -> RecordSutinSpecificFields.FondInstallationLogbook.FOND_ID == record.getFond().getId())
                .toList();

        Map<LocalDate, List<RecordRow>> groupedInstallationLogbooksByDate = groupRecordsByDate(installationLogbooks).entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> ListUtil.convert(entry.getValue(), record -> recordsToRecordRowsConverter.convertSingle(record, currentAuth, ctx, locale))
                ));

        return new GroupedProjectItemsResponse(uniqueFonds, otherItems, groupedInstallationLogbooksByDate);
    }

    private Map<LocalDate, List<Record>> groupRecordsByDate(List<Record> records) {
        return records.stream()
                .filter((record) -> RecordSutinSpecificFields.FondInstallationLogbook.FOND_ID == record.getFond().getId())
                .collect(Collectors.groupingBy(record ->
                        RecordSutinSpecificFields.FondInstallationLogbook.Date.Value.FIELD_FINDER
                                .getFirstIn(record.getDetail(), LocalDateFieldValue::extract)
                ));
    }
}
