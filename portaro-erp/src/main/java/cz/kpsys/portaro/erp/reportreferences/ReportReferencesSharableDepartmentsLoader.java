package cz.kpsys.portaro.erp.reportreferences;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class ReportReferencesSharableDepartmentsLoader implements ContextualProvider<Department, @NonNull List<Department>> {

    @NonNull ContextualProvider<Department, @NonNull List<Integer>> holdingsAssignableFromContextsContextualProvider;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;

    @Override
    @NonNull
    public List<Department> getOn(Department ctx) {
        return holdingsAssignableFromContextsContextualProvider.getOn(ctx).stream()
                .map(departmentLoader::getById)
                .toList();
    }
}